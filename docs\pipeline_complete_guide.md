# Common Requirements 需求分析流水线完整说明

## 概述

Common Requirements 是一个基于大语言模型的智能需求分析系统，专门用于软件产品线的需求提取、聚类、分解和可变性分析。该系统通过六个核心阶段的流水线处理，将原始需求文档转换为结构化的可变性配置项，为软件产品线工程提供强大的需求分析和配置管理支持。

## 系统架构

### 整体流水线架构

```mermaid
graph TD
    A[需求文档<br/>PDF/DOCX] --> B[文档解析模块<br/>document_reader.py]
    B --> C[需求聚类模块<br/>cluster_similar_reqs.py]
    C --> D[需求分解模块<br/>requirement_decomposer.py]
    D --> E[动作聚类模块<br/>cluster_and_standardize_actions.py]
    E --> F[语义分析模块<br/>analyze_semantic_role.py]
    F --> G[可变性分析模块<br/>generate_comparison_matrix.py]
    G --> H[配置项输出<br/>JSON格式]
    
    I[配置管理<br/>config.py] --> B
    I --> C
    I --> D
    I --> E
    I --> F
    I --> G
    
    J[主程序<br/>__main__.py] --> B
    J --> C
    J --> D
    J --> E
    J --> F
    J --> G
```

### 技术栈

- **核心技术**: Python 3.8+
- **AI 能力**: OpenAI API (DeepSeek, GPT-4等)
- **NLP 工具**: HanLP, SentenceTransformers
- **机器学习**: BERTopic, HDBSCAN, UMAP
- **数据处理**: Pandas, NumPy
- **文档处理**: pdfplumber, python-docx

## 完整流水线详解

### 阶段一：需求文档解析与提取

#### 模块：`document_reader.py`

**功能**：从 PDF 和 DOCX 格式的需求文档中智能提取功能性需求。

**核心组件**：
- `RequirementExtractor` 类：主要的需求提取器
- 滑动窗口技术：处理大型文档
- LLM 智能识别：区分功能性和非功能性需求

**处理流程**：
```python
# 1. 文档加载
raw_text = self.load_file(file_path)

# 2. 文本窗口化
windows = self._create_sliding_windows(raw_text)

# 3. LLM 需求提取
for window in windows:
    response = self._call_llm(window)
    requirements = self._parse_llm_response(response)

# 4. 去重和保存
unique_reqs = self._deduplicate_requirements(all_requirements)
self._save_to_jsonl(unique_reqs)
```

**输入**：
- PDF/DOCX 需求文档
- API 密钥配置

**输出**：
- `{文档名}_requirements.jsonl` 格式文件
- 每行一个需求对象：
```json
{
    "req_id": "REQ_001",
    "req_name": "用户认证需求",
    "req_content": "系统应支持用户身份验证功能..."
}
```

**关键特性**：
- 多格式文档支持（PDF、DOCX）
- 滑动窗口处理大文档
- 智能去重机制
- 完整的错误处理

### 阶段二：相似需求聚类分析

#### 模块：`cluster_similar_reqs.py`

**功能**：识别和聚类具有相似功能的需求，特别是软件产品线中的功能变体。

**核心组件**：
- 双模式聚类支持：BERTopic + LLM 智能聚类
- 功能变体识别：基于语义理解的相似需求归类
- 多源数据处理：支持来自不同系统的需求整合

**BERTopic 聚类流程**：
```python
# 1. 模型初始化
embedding_model = SentenceTransformer(EMBEDDING_MODEL)
umap_model = UMAP(n_neighbors=10, n_components=5)
cluster_model = HDBSCAN(min_cluster_size=3)
topic_model = BERTopic(embedding_model=embedding_model, ...)

# 2. 主题建模
topics, _ = topic_model.fit_transform(texts)

# 3. 结果组织
topics_dict = defaultdict(list)
for doc, topic in zip(abstracts, topics):
    topics_dict[topic].append(doc)
```

**LLM 聚类流程**：
```python
# 1. 逐个需求分析
for requirement in abstracts:
    analysis = _call_llm_for_function_matching(requirement, existing_groups)
    
    # 2. 功能变体判断
    if matched_group_id in function_groups:
        # 归入现有组
        function_groups[matched_group_id]['requirements'].append(requirement)
    else:
        # 创建新组
        function_groups[next_group_id] = new_group
```

**输入**：
- JSONL 格式的需求文件（多个）
- 可配置的相似度阈值

**输出**：
- `topic_clustering_results*.json` 格式文件
- 聚类结果包含：
```json
{
    "metadata": {
        "total_requirements": 150,
        "topic_groups": 12,
        "similar_topic_groups": 8,
        "clustering_method": "LLM-based analysis"
    },
    "similar_topic_groups": {
        "1": {
            "core_function": "用户身份认证",
            "requirements": [...]
        }
    }
}
```

**关键特性**：
- 支持 BERTopic 和 LLM 两种聚类方法
- 智能的功能变体识别
- 保持完整的溯源信息
- 可配置的相似度阈值

### 阶段三：需求结构化分解

#### 模块：`requirement_decomposer.py`

**功能**：将复杂需求分解为结构化的输入、输出和处理步骤组件。

**核心组件**：
- `RequirementDecomposer` 类：主要的分解器
- 智能重试机制：应对 API 限制
- 状态管理：支持断点续传

**分解流程**：
```python
# 1. LLM Prompt 设计
system_prompt = """
你是一个专业的需求分析师，负责将软件需求拆解为结构化组件。
请将需求拆解为三个部分：
1. 输入(Input): 系统接收的数据或触发事件
2. 输出(Output): 系统产生的数据或响应  
3. 需求动作序列(Actions): 系统完成该需求的处理步骤

输出格式：
{
  "input": ["输入项1", "输入项2"],
  "output": ["输出项1", "输出项2"], 
  "actions": ["动作1", "动作2"]
}
"""

# 2. 需求分解
for requirement in group_requirements:
    decomposed = self.decompose_requirement(requirement)
    group_result["decomposed_requirements"].append(decomposed)
```

**输入**：
- 聚类结果 JSON 文件
- 功能分组的需求数据

**输出**：
- `decomposed_*.json` 格式文件
- 分解后的需求结构：
```json
{
    "decomposed_function_groups": {
        "1": {
            "core_function": "用户身份认证",
            "decomposed_requirements": [
                {
                    "req_id": "REQ_001",
                    "req_content": "原始需求内容",
                    "input": ["用户凭证", "认证请求"],
                    "output": ["认证结果", "用户token"],
                    "processing_steps": [
                        "系统接收用户凭证",
                        "系统验证用户身份", 
                        "系统生成认证token",
                        "系统返回认证结果"
                    ]
                }
            ]
        }
    }
}
```

**关键特性**：
- 基于 LLM 的智能分解
- 保持原始需求意图完整性
- 支持批量和单个需求处理
- 完善的状态跟踪机制

### 阶段四：动作聚类与标准化

#### 模块：`cluster_and_standardize_actions.py`

**功能**：对分解后的处理步骤进行两阶段处理：动作聚类和标准化。

**核心组件**：
- 第一阶段：`cluster_processing_steps()` - 动作聚类
- 第二阶段：`standardize_action_clusters()` - 动作标准化
- 智能排序策略：优先处理简单需求

**两阶段处理流程**：

**第一阶段 - 动作聚类**：
```python
# 1. 步骤收集与智能排序
all_steps = []
for req in decomposed_reqs:
    for step in req["processing_steps"]:
        all_steps.append({
            "step": step,
            "req_step_count": len(req["processing_steps"])
        })

# 按复杂度排序：优先处理简单需求
all_steps.sort(key=lambda x: x["req_step_count"])

# 2. LLM 聚类分析
for step_info in all_steps:
    analysis = _call_llm_for_action_clustering(step_info["step"], action_clusters)
    
    if analysis["is_same_action"] and analysis["matched_cluster_id"] in action_clusters:
        # 归入现有类别
        action_clusters[matched_cluster_id]['steps'].append(step_info)
    else:
        # 创建新类别
        action_clusters[next_cluster_id] = new_cluster
```

**第二阶段 - 动作标准化**：
```python
# 对每个聚类进行标准化
for cluster_id, cluster_info in action_clusters.items():
    standardization = _call_llm_for_standardization(cluster_info['steps'])
    
    standardized_actions[standardized_action] = {
        "steps_with_source": cluster_info['steps'],
        "req_sources": [...],
        "step_count": len(cluster_info['steps']),
        "cluster_id": cluster_id
    }
```

**输入**：
- 分解后的需求数据
- 可配置的相似度阈值

**输出**：
- `cluster_and_standardized_reqs_*.json`
- 标准化动作映射：
```json
{
    "standardized_actions": {
        "用户身份验证": {
            "steps_with_source": [
                {
                    "step": "系统验证用户身份",
                    "req_id": "REQ_001",
                    "original_index": 1
                }
            ],
            "req_sources": ["REQ_001", "REQ_002"],
            "step_count": 3
        }
    }
}
```

**关键特性**：
- 两阶段处理架构
- 上位词/下位词识别
- 业务特异性保持
- 完整的溯源信息

### 阶段五：语义角色标注分析

#### 模块：`analyze_semantic_role.py`

**功能**：对标准化动作进行语义角色标注，识别动作的语义结构。

**核心组件**：
- 双模式分析：HanLP + LLM 语义分析
- 语义角色体系：ARG0-ARG4, ARGM-*
- 批量处理能力：支持分组和单组数据

**语义角色定义**：
```python
SEMANTIC_ROLES = [
    "ARG0",      # 施事者（动作执行者）
    "ARG1",      # 受事者（动作对象）
    "ARG2",      # 起点、来源、材料等
    "ARG3",      # 终点、目标、受益者等
    "ARG4",      # 其他补语
    "ARGM-CND",  # 条件
    "ARGM-LOC",  # 地点  
    "ARGM-MNR",  # 方式
    "ARGM-TMP"   # 时间
]
```

**HanLP 分析流程**：
```python
# 1. 初始化完整管道
hanlp_pipeline = hanlp.load('CLOSE_TOK_POS_NER_SRL_DEP_SDP_CON_ELECTRA_SMALL_ZH')

# 2. 语义角色标注
result = hanlp_pipeline(step_text)
srl_result = result.get('srl', [])

# 3. 转换为标准元组格式
for predicate_info in srl_result:
    tuple_parts = [predicate]
    for role in SEMANTIC_ROLES:
        if role in args:
            tuple_parts.extend([role, args[role]])
    step_tuples.append(tuple(tuple_parts))
```

**LLM 分析流程**：
```python
system_prompt = """
你是一个专业的语义角色标注专家。
请分析文本中的语义角色，并以JSON数组格式返回：
[
    ["谓词1", "ARG0", "施事者", "ARG1", "受事者", "ARGM-MNR", "方式"],
    ["谓词2", "ARG0", "施事者", "ARG1", "受事者"]
]
"""

step_srl_tuples = _call_llm_for_step_srl(step_text)
```

**输入**：
- 标准化动作数据
- 选择分析模式（HanLP/LLM）

**输出**：
- `semantic_roles_*.json`
- 带语义角色的数据：
```json
{
    "standardized_actions": {
        "用户身份验证": {
            "steps_with_source": [
                {
                    "step": "系统验证用户身份",
                    "semantic_tuples": [
                        ["验证", "ARG0", "系统", "ARG1", "用户身份", "ARGM-MNR", "自动"]
                    ]
                }
            ]
        }
    }
}
```

**关键特性**：
- 支持 HanLP 和 LLM 两种分析方式
- 标准化的语义角色体系
- 自动化的批量处理
- 错误容忍和异常处理

### 阶段六：可变性配置项生成

#### 模块：`generate_comparison_matrix.py`

**功能**：从语义角色中提取配置变化点，生成软件产品线的可变性配置项。

**核心组件**：
- 语义元组提取：`extract_semantic_tuples()`
- 矩阵转换：`expand_tuples_to_matrix()`
- LLM 参数分析：`compare_parameters_with_llm()`

**处理流程**：

**1. 语义元组提取**：
```python
# 从每个动作提取所有语义元组
semantic_tuples_by_action = {}
for action_name, action_data in standardized_actions.items():
    all_tuples = []
    for step_info in action_data["steps_with_source"]:
        all_tuples.extend(step_info.get("semantic_tuples", []))
    semantic_tuples_by_action[action_name] = all_tuples
```

**2. 矩阵展开**：
```python
# 将语义元组转换为二维DataFrame
def expand_tuples_to_matrix(semantic_tuples):
    # 收集所有参数类型
    all_arg_types = set()
    for tuple_data in semantic_tuples:
        for i in range(1, len(tuple_data), 2):
            all_arg_types.add(tuple_data[i])
    
    # 创建DataFrame
    columns = ["predicate"] + sorted(list(all_arg_types))
    matrix_data = []
    for tuple_data in semantic_tuples:
        row = {"predicate": tuple_data[0]}
        # 填充参数
        for i in range(1, len(tuple_data), 2):
            if i + 1 < len(tuple_data):
                row[tuple_data[i]] = tuple_data[i + 1]
        matrix_data.append(row)
    
    return pd.DataFrame(matrix_data, columns=columns)
```

**3. LLM 配置分析**：
```python
system_prompt = """
你是一个需求分析专家，负责从给定的参数值中归纳出配置项。

重要原则：
1. 严格基于提供的参数值进行分析
2. 从参数值的核心差异点归纳配置维度
3. 候选项应该是互斥的、不重复的
4. 保持业务特异性

返回JSON格式：
[
    {
        "配置项名称": "数据采集对象",
        "候选项": ["飞行姿态数据", "速度数据", "位置数据"]
    }
]
"""

for param_type in param_types_to_compare:
    unique_values = df[param_type].unique()
    config_result = _call_llm_for_config_analysis(unique_values)
```

**输入**：
- 语义角色标注数据
- 可配置的分析参数

**输出**：
- `variability_analysis_*.json`
- 可变性配置项：
```json
{
    "standardized_actions": {
        "用户身份验证": {
            "variability_config": {
                "ARG1": [
                    {
                        "配置项名称": "认证对象类型",
                        "候选项": ["用户凭证", "系统证书", "生物特征"]
                    }
                ],
                "ARGM-MNR": [
                    {
                        "配置项名称": "验证方式",
                        "候选项": ["密码验证", "双因子认证", "生物识别"]
                    }
                ]
            }
        }
    }
}
```

**关键特性**：
- 基于语义角色的参数提取
- 智能的配置维度识别
- 互斥候选项生成
- 业务特异性保持

## 配置管理与环境设置

### 配置文件：`config.py`

**核心配置**：
```python
# 路径配置
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
DATA_DIR = os.path.join(PROJECT_ROOT, 'data')
MODEL_DIR = os.path.join(PROJECT_ROOT, 'models')
OUTPUT_DIR = os.path.join(PROJECT_ROOT, 'output')

# 服务配置
BASE_URL = 'https://api.deepseek.com'

# 模型配置
EMBEDDING_MODEL = os.path.join(MODEL_DIR, "sentence_transformers", "BAAI_bge-base-zh-v1.5")
SRL_MODEL = 'CLOSE_TOK_POS_NER_SRL_DEP_SDP_CON_ELECTRA_SMALL_ZH'

# 环境变量设置
os.environ["HANLP_HOME"] = os.path.join(MODEL_DIR, "hanlp")
```

### 环境准备

**1. 依赖安装**：
```bash
pip install openai hanlp sentence-transformers bertopic
pip install pdfplumber python-docx pandas numpy
pip install umap-learn hdbscan scikit-learn tqdm
```

**2. 环境变量设置**：
```bash
# Windows
set DEEPSEEK_API_KEY=sk-your-key-here

# Linux/Mac  
export DEEPSEEK_API_KEY=sk-your-key-here
```

**3. 目录结构创建**：
```bash
mkdir -p data/commonreq
mkdir -p models/{hanlp,sentence_transformers}
mkdir -p output/{requirements,commonReq,decomposed_requirements,comparison_matrices}
mkdir -p docs
mkdir -p logs
```

## 主程序执行：`__main__.py`

### 完整工作流程

```python
# 执行命令
python -m commonReq

# 或直接运行
python commonReq/__main__.py
```

### 流程编排

```python
if __name__ == "__main__":
    api_key = os.getenv("DEEPSEEK_API_KEY")
    
    # 阶段1: 需求文档提取 (可选)
    # extractor = RequirementExtractor(api_key=api_key)
    # result_count = extractor.extract_requirements(example_file)
    
    # 阶段2: 需求聚类分析 (可选)
    # clustered_reqs = cluster_similar_requirements_llm(input_dir, api_key)
    
    # 阶段3: 需求分解 (可选) 
    # decomposer = RequirementDecomposer(api_key=api_key)
    # decomposer.process_requirements_from_cluster(cluster_file)
    
    # 阶段4: 动作聚类与标准化 (可选)
    # result = cluster_and_standardize_actions(decomposed_req, api_key)
    
    # 阶段5: 语义角色分析 (可选)
    # run_semantic_role_analysis(action_result, use_llm=True, api_key=api_key)
    
    # 阶段6: 可变性分析 (当前活跃)
    print("开始配置项分析...")
    semantic_roles_req = r'output\commonReq\new_semantic_roles.json'
    if os.path.exists(semantic_roles_req):
        with open(semantic_roles_req, 'r', encoding='utf-8') as f:
            semantic_data = json.load(f)
        run_analyze_requirement_variability(semantic_data, api_key=api_key)
```

## 数据流和文件格式

### 完整数据流向

```
原始文档 (PDF/DOCX)
    ↓ document_reader
JSONL 需求文件
    ↓ cluster_similar_reqs  
聚类分组 JSON
    ↓ requirement_decomposer
结构化需求 JSON
    ↓ cluster_and_standardize_actions
标准化动作 JSON
    ↓ analyze_semantic_role
语义角色 JSON
    ↓ generate_comparison_matrix
可变性配置 JSON
```

### 关键文件格式

**1. 需求提取输出** (`*.jsonl`)：
```json
{"req_id": "REQ_001", "req_name": "用户认证", "req_content": "系统应支持..."}
{"req_id": "REQ_002", "req_name": "数据加密", "req_content": "系统必须对..."}
```

**2. 聚类结果** (`topic_clustering_results*.json`)：
```json
{
    "metadata": {"total_requirements": 100, "topic_groups": 8},
    "similar_topic_groups": {
        "1": {
            "core_function": "用户管理",
            "requirements": [...]
        }
    }
}
```

**3. 需求分解** (`decomposed_*.json`)：
```json
{
    "decomposed_function_groups": {
        "1": {
            "core_function": "用户管理", 
            "decomposed_requirements": [
                {
                    "req_id": "REQ_001",
                    "input": ["用户信息", "操作请求"],
                    "output": ["处理结果", "状态反馈"],
                    "processing_steps": ["接收请求", "验证权限", "执行操作", "返回结果"]
                }
            ]
        }
    }
}
```

**4. 动作标准化** (`cluster_and_standardized_reqs*.json`)：
```json
{
    "decomposed_function_groups": {
        "1": {
            "standardized_actions": {
                "用户身份验证": {
                    "steps_with_source": [...],
                    "req_sources": ["REQ_001", "REQ_002"],
                    "step_count": 5
                }
            }
        }
    }
}
```

**5. 语义角色** (`semantic_roles*.json`)：
```json
{
    "standardized_actions": {
        "用户身份验证": {
            "steps_with_source": [
                {
                    "step": "系统验证用户身份",
                    "semantic_tuples": [
                        ["验证", "ARG0", "系统", "ARG1", "用户身份"]
                    ]
                }
            ]
        }
    }
}
```

**6. 可变性配置** (`variability_analysis*.json`)：
```json
{
    "standardized_actions": {
        "用户身份验证": {
            "variability_config": {
                "ARG1": [
                    {
                        "配置项名称": "认证方式",
                        "候选项": ["密码认证", "生物识别", "双因子认证"]
                    }
                ]
            }
        }
    }
}
```

## 系统特性和优势

### 1. 智能化处理
- **LLM 驱动**：全流程基于大语言模型的智能分析
- **语义理解**：深度理解需求的语义结构和意图
- **自动化程度高**：最小化人工干预，提高处理效率

### 2. 灵活性和扩展性
- **模块化架构**：各阶段独立且可配置
- **多种算法支持**：BERTopic + LLM 双模式选择
- **可配置参数**：丰富的配置选项适应不同场景

### 3. 产品线工程支持
- **功能变体识别**：专门针对软件产品线的相似功能识别
- **配置项生成**：自动生成结构化的可变性配置
- **溯源信息完整**：保持从原始需求到配置项的完整追踪

### 4. 质量保证
- **多层错误处理**：API 重试、数据验证、异常恢复
- **状态管理**：支持断点续传和增量处理
- **结果验证**：多维度的质量检查机制

### 5. 易用性
- **标准化接口**：统一的数据格式和调用方式
- **完整文档**：详细的设计文档和使用说明
- **配置管理**：集中化的配置管理机制

## 应用场景

### 1. 软件产品线工程
- **需求变体分析**：识别产品线中的功能变体和差异点
- **配置管理**：生成产品线的可变性配置方案
- **架构设计**：为可变性架构设计提供需求基础

### 2. 需求工程
- **需求文档分析**：自动化的需求提取和分类
- **需求重用**：识别可重用的需求模式
- **需求质量评估**：通过结构化分析提升需求质量

### 3. 系统集成
- **跨系统需求分析**：整合来自多个系统的需求
- **接口设计**：基于需求分解设计系统接口
- **测试用例生成**：为结构化需求生成测试用例

### 4. 敏捷开发
- **用户故事分解**：将复杂用户故事分解为具体任务
- **迭代规划**：基于需求分析进行迭代内容规划
- **验收标准**：生成明确的验收标准和测试点

## 性能指标和限制

### 性能指标
- **处理速度**：单个需求分解时间 < 30秒
- **准确率**：需求提取准确率 > 85%
- **聚类质量**：相似需求识别准确率 > 80%
- **扩展性**：支持 1000+ 需求的批量处理

### 系统限制
- **API 依赖**：依赖外部 LLM API 服务
- **语言支持**：当前主要支持中文需求文档
- **文档格式**：限制为 PDF 和 DOCX 格式
- **计算资源**：大规模处理需要充足的内存和网络带宽

### 使用建议
- **API 配额管理**：合理规划 API 调用频率和数量
- **数据预处理**：确保需求文档的格式规范性
- **结果验证**：建议人工抽样验证关键结果
- **增量更新**：利用状态管理机制进行增量处理

## 扩展和定制

### 功能扩展方向
1. **多语言支持**：扩展对英文等其他语言的支持
2. **更多文档格式**：支持 Word、RTF、HTML 等格式
3. **实时处理**：支持流式需求处理和实时分析
4. **可视化界面**：开发 Web 界面和图形化展示
5. **集成能力**：与现有需求管理工具集成

### 定制化配置
1. **领域适配**：针对特定业务领域优化分析模型
2. **企业集成**：与企业现有系统和流程集成
3. **质量标准**：根据组织质量标准定制验证规则
4. **输出格式**：支持多种输出格式和报告模板

### 开发者扩展
1. **插件机制**：支持自定义分析插件
2. **API 接口**：提供标准的 RESTful API
3. **数据连接器**：支持多种数据源连接
4. **模型训练**：支持基于企业数据的模型微调

## 总结

Common Requirements 需求分析流水线为软件产品线工程提供了完整的自动化需求分析解决方案。通过六个紧密协作的处理阶段，系统能够将原始的需求文档转换为结构化的可变性配置项，大大提高了需求分析的效率和质量。

系统的核心优势在于：
- **端到端自动化**：从文档解析到配置生成的完整流程自动化
- **智能化分析**：基于先进 LLM 技术的深度语义理解
- **产品线专用**：专门针对软件产品线工程的功能变体识别
- **高度可配置**：灵活的模块化架构适应不同应用场景

通过持续的优化和扩展，该系统将为软件工程领域的需求分析和产品线管理提供更强大的技术支持。
