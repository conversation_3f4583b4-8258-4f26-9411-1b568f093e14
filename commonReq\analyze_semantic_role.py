import json
import os
import re
from typing import List, Dict, Any
import hanlp
import pandas as pd
from sentence_transformers import SentenceTransformer
from umap import UMAP
from hdbscan import HDBSCAN
from sklearn.cluster import KMeans
from sklearn.feature_extraction.text import CountVectorizer
from bertopic.representation import KeyBERTInspired
from bertopic import BERTopic
from sentence_transformers import SentenceTransformer
from collections import defaultdict
from openai import OpenAI
import time
from commonReq.cluster_similar_reqs import cluster_similar_requirements
from commonReq.config import MODEL_DIR,DOCS_DIR, OUTPUT_DIR,logging,EMBEDDING_MODEL,SRL_MODEL,COMMONREQ_DATA

save_to = os.path.join(MODEL_DIR, "bertopic")

# 那些在语义角色标注中常见的角色
SEMANTIC_ROLES = [
    "ARG0", "ARG1", "ARG2", "ARG3", "ARG4",
    "ARGM-CND", "ARGM-LOC", "ARGM-MNR","ARGM-TMP"
]

def analyze_semantic_roles(standardized_data, save_results=True):
    """
    使用HanLP识别语义角色
    对标准化动作数据中每个动作的处理步骤进行语义角色标注分析（使用 HanLP 模型）
    
    Args:
        standardized_data: 包含standardized_actions的数据字典
        save_results: 是否保存结果到文件
        
    Returns:
        dict: 更新后的数据，包含语义角色分析结果
    """
    logging.info("=== 语义角色分析（使用HanLP） ===")
    
    # 初始化 HanLP 的完整管道（包含语义角色标注）
    try:
        # 使用包含语义角色标注的完整管道
        hanlp_pipeline = hanlp.load('CLOSE_TOK_POS_NER_SRL_DEP_SDP_CON_ELECTRA_SMALL_ZH')
        logging.info("HanLP 完整管道加载成功")
    except Exception as e:
        logging.error(f"HanLP 完整管道加载失败: {e}")
        return standardized_data
    
    def _call_hanlp_for_step_srl(step_text: str) -> List[tuple]:
        """调用 HanLP 进行单个步骤的语义角色分析，返回语义角色元组列表"""
        try:
            # 使用完整管道进行分析
            result = hanlp_pipeline(step_text)
            logging.debug(f"HanLP 完整分析结果: {result}")
            
            # 提取SRL结果
            srl_result = result.get('srl', [])
            logging.debug(f"HanLP SRL result for step: {step_text}\n{srl_result}")
            
            # 转换HanLP结果为元组列表格式
            step_tuples = []
            
            # srl_result 是一个列表，每个元素是一个谓词的语义角色
            for predicate_info in srl_result:
                pred = ""
                args = {}  # 存储角色类型和内容的映射
                
                # predicate_info 是一个包含 (词, 角色, 开始位置, 结束位置) 元组的列表
                for word, role, start, end in predicate_info:
                    if role == "PRED":
                        pred = word
                    elif role in SEMANTIC_ROLES:
                        # 如果已存在该角色，合并内容
                        if role in args:
                            args[role] = args[role] + " " + word.replace(" ", "")
                        else:
                            args[role] = word.replace(" ", "")
                
                # 构建元组：谓词 + 角色类型和内容交替出现
                if pred:
                    tuple_parts = [pred]
                    for role in SEMANTIC_ROLES:
                        if role in args and args[role]:
                            tuple_parts.extend([role, args[role]])
                    
                    if len(tuple_parts) > 1:  # 至少有谓词和一个角色
                        step_tuples.append(tuple(tuple_parts))
            
            return step_tuples
            
        except Exception as e:
            logging.error(f"HanLP语义角色分析失败: {step_text[:100]}...\n错误: {e}")
            return []
    
    # 复制输入数据以避免修改原始数据
    result_data = json.loads(json.dumps(standardized_data))
    
    # 检查输入数据格式
    if "standardized_actions" not in result_data:
        logging.error("输入数据中缺少 'standardized_actions' 字段")
        return result_data

    # 对每个标准化动作的处理步骤进行语义角色分析
    standardized_actions = result_data["standardized_actions"]
    total_actions = len(standardized_actions)
    logging.info(f"开始分析 {total_actions} 个标准化动作的语义角色...")
    
    for action_idx, (action_name, action_data) in enumerate(standardized_actions.items()):
        logging.info(f"\n分析动作 {action_idx + 1}/{total_actions}: {action_name}")
        
        if "steps_with_source" not in action_data:
            logging.warning(f"动作 {action_name} 缺少 steps_with_source 字段")
            continue
            
        steps_with_source = action_data["steps_with_source"]
        if not steps_with_source:
            logging.info(f"动作 {action_name} 的 steps_with_source 为空")
            continue
            
        logging.info(f"分析 {len(steps_with_source)} 个处理步骤的语义角色...")
        
        # 对每个步骤进行语义角色分析
        for step_idx, step_info in enumerate(steps_with_source):
            step_text = step_info["step"]
            req_id = step_info["req_id"]
            
            logging.info(f"  - 步骤 {step_idx + 1}/{len(steps_with_source)} ({req_id}): {step_text[:60]}...")
            
            # 调用HanLP分析单个步骤
            step_srl_tuples = _call_hanlp_for_step_srl(step_text)
            
            if step_srl_tuples:
                logging.info(f"    发现 {len(step_srl_tuples)} 个语义角色元组")
                for tuple_idx, srl_tuple in enumerate(step_srl_tuples):
                    logging.debug(f"      元组 {tuple_idx + 1}: {srl_tuple}")
                # 将语义角色元组添加到步骤信息中
                step_info["semantic_tuples"] = step_srl_tuples
            else:
                logging.info("    未发现语义角色")
                step_info["semantic_tuples"] = []

    # 统计总体结果
    total_tuples = 0
    total_steps = 0
    for action_data in standardized_actions.values():
        if "steps_with_source" in action_data:
            for step_info in action_data["steps_with_source"]:
                if "semantic_tuples" in step_info:
                    total_tuples += len(step_info["semantic_tuples"])
                    total_steps += 1

    logging.info(f"\n=== 语义角色分析完成 ===")
    logging.info(f"总共分析了 {total_actions} 个标准化动作")
    logging.info(f"总共分析了 {total_steps} 个处理步骤")
    logging.info(f"总共识别了 {total_tuples} 个语义角色元组")

    if save_results:
        # 保存结果到JSON文件
        logging.info("保存语义角色分析结果...")
        os.makedirs(os.path.join(OUTPUT_DIR, "commonReq"), exist_ok=True)
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(OUTPUT_DIR, "commonReq", f"semantic_roles_hanlp_{timestamp}.json")
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)
            logging.info(f"语义角色分析结果已保存到: {output_file}")
        except Exception as e:
            logging.error(f"保存语义角色分析结果失败: {e}")

    return result_data


def analyze_semantic_roles_llm(standardized_data, api_key, model="deepseek-chat", save_results=True, max_retries=3, delay=2):
    """
    使用大模型识别语义角色
    对标准化动作数据中每个动作的处理步骤进行语义角色标注分析（使用 OpenAI GPT 模型）
    
    Args:
        standardized_data: 包含standardized_actions的数据字典
        api_key: OpenAI API 密钥
        model: 使用的 GPT 模型名称
        save_results: 是否保存结果到文件
        max_retries: API 调用最大重试次数
        delay: 重试间隔时间（秒）
        
    Returns:
        dict: 更新后的数据，包含语义角色分析结果
    """
    logging.info("=== 语义角色分析（使用大模型） ===")
    
    # 初始化 OpenAI 客户端
    try:
        from commonReq.config import BASE_URL
        client = OpenAI(api_key=api_key, base_url=BASE_URL)
        logging.info("OpenAI 客户端初始化成功")
    except Exception as e:
        logging.error(f"OpenAI 客户端初始化失败: {e}")
        return standardized_data

    def _call_llm_for_step_srl(step_text: str) -> List[tuple]:
        """调用 LLM 进行单个步骤的语义角色分析，返回语义角色元组列表"""
        system_prompt = """你是一个专业的语义角色标注专家。努力拆解给定处理步骤文本中的语义角色。

语义角色定义：
- ARG0: 施事者（通常是动作的执行者）
- ARG1: 受事者（通常是动作的接受者或对象）
- ARG2: 起点、来源、材料等
- ARG3: 终点、目标、受益者等
- ARG4: 其他补语
- ARGM-CND: 条件
- ARGM-LOC: 地点
- ARGM-MNR: 方式
- ARGM-TMP: 时间

请分析文本中的语义角色，并以JSON数组格式返回，每个元素是一个包含谓词和语义角色的元组：
[
    ["谓词1", "ARG0", "施事者", "ARG1", "受事者", "ARGM-MNR", "方式"],
    ["谓词2", "ARG0", "施事者", "ARG1", "受事者"]
]

要求：
1. 每个元组第一个元素是谓词
2. 后续元素按 角色类型-角色内容 的格式成对出现
3. 如果没有某个角色则省略
4. 只输出JSON数组，不要包含其他内容
5. 不可返回空数组[]"""

        user_prompt = f"请分析以下处理步骤的语义角色：\n\n{step_text}"
        
        for attempt in range(max_retries):
            try:
                response = client.chat.completions.create(
                    model=model,
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt}
                    ],
                    temperature=0.1,
                    max_tokens=1000,
                    timeout=30
                )
                
                content = response.choices[0].message.content
                if not content:
                    logging.warning(f"API 返回空内容，步骤: {step_text[:50]}")
                    return []
                
                # 尝试解析JSON
                try:
                    result = json.loads(content.strip())
                    if isinstance(result, list):
                        # 验证结果格式
                        valid_tuples = []
                        for item in result:
                            if isinstance(item, list) and len(item) >= 1:
                                valid_tuples.append(tuple(item))
                        return valid_tuples
                    return []
                except json.JSONDecodeError:
                    # 尝试提取JSON部分
                    json_match = re.search(r'\[.*\]', content.strip(), re.DOTALL)
                    if json_match:
                        try:
                            result = json.loads(json_match.group(0))
                            if isinstance(result, list):
                                valid_tuples = []
                                for item in result:
                                    if isinstance(item, list) and len(item) >= 1:
                                        valid_tuples.append(tuple(item))
                                return valid_tuples
                        except json.JSONDecodeError:
                            pass
                    
                    logging.warning(f"无法解析JSON响应: {content[:200]}...")
                    return []
                    
            except Exception as e:
                error_msg = str(e).lower()
                if attempt < max_retries - 1:
                    if "rate limit" in error_msg:
                        wait_time = delay * (2 ** attempt)
                        logging.warning(f"API 速率限制，等待 {wait_time} 秒后重试...")
                        time.sleep(wait_time)
                    elif "timeout" in error_msg or "api" in error_msg:
                        wait_time = delay * (attempt + 1)
                        logging.warning(f"API 错误，等待 {wait_time} 秒后重试...")
                        time.sleep(wait_time)
                    else:
                        logging.warning(f"未知错误，重试中: {e}")
                        time.sleep(delay)
                else:
                    logging.error(f"API 调用失败: {e}")
                    return []
        
        logging.error("达到最大重试次数")
        return []

    # 复制输入数据以避免修改原始数据
    result_data = json.loads(json.dumps(standardized_data))
    
    # 检查输入数据格式
    if "standardized_actions" not in result_data:
        logging.error("输入数据中缺少 'standardized_actions' 字段")
        return result_data

    # 对每个标准化动作的处理步骤进行语义角色分析
    standardized_actions = result_data["standardized_actions"]
    total_actions = len(standardized_actions)
    logging.info(f"开始分析 {total_actions} 个标准化动作的语义角色...")
    
    for action_idx, (action_name, action_data) in enumerate(standardized_actions.items()):
        logging.info(f"\n分析动作 {action_idx + 1}/{total_actions}: {action_name}")
        
        if "steps_with_source" not in action_data:
            logging.warning(f"动作 {action_name} 缺少 steps_with_source 字段")
            continue
            
        steps_with_source = action_data["steps_with_source"]
        if not steps_with_source:
            logging.info(f"动作 {action_name} 的 steps_with_source 为空")
            continue
            
        logging.info(f"分析 {len(steps_with_source)} 个处理步骤的语义角色...")
        
        # 对每个步骤进行语义角色分析
        for step_idx, step_info in enumerate(steps_with_source):
            step_text = step_info["step"]
            req_id = step_info["req_id"]
            
            logging.info(f"  - 步骤 {step_idx + 1}/{len(steps_with_source)} ({req_id}): {step_text[:60]}...")
            
            # 调用LLM分析单个步骤
            step_srl_tuples = _call_llm_for_step_srl(step_text)
            
            if step_srl_tuples:
                logging.info(f"    发现 {len(step_srl_tuples)} 个语义角色元组")
                for tuple_idx, srl_tuple in enumerate(step_srl_tuples):
                    logging.debug(f"      元组 {tuple_idx + 1}: {srl_tuple}")
                # 将语义角色元组添加到步骤信息中
                step_info["semantic_tuples"] = step_srl_tuples
            else:
                logging.info("    未发现语义角色")
                step_info["semantic_tuples"] = []
            
            # 添加延迟避免API限制
            time.sleep(0.5)

    # 统计总体结果
    total_tuples = 0
    total_steps = 0
    for action_data in standardized_actions.values():
        if "steps_with_source" in action_data:
            for step_info in action_data["steps_with_source"]:
                if "semantic_tuples" in step_info:
                    total_tuples += len(step_info["semantic_tuples"])
                    total_steps += 1

    logging.info(f"\n=== 语义角色分析完成 ===")
    logging.info(f"总共分析了 {total_actions} 个标准化动作")
    logging.info(f"总共分析了 {total_steps} 个处理步骤")
    logging.info(f"总共识别了 {total_tuples} 个语义角色元组")

    if save_results:
        # 保存结果到JSON文件
        logging.info("保存语义角色分析结果...")
        os.makedirs(os.path.join(OUTPUT_DIR, "commonReq"), exist_ok=True)
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(OUTPUT_DIR, "commonReq", f"semantic_roles_analysis_{timestamp}.json")
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)
            logging.info(f"语义角色分析结果已保存到: {output_file}")
        except Exception as e:
            logging.error(f"保存语义角色分析结果失败: {e}")

    return result_data


def run_semantic_role_analysis(clustered_data, 
                               use_llm=False, 
                               api_key=None, 
                               model="deepseek-chat", 
                               save_results=True):
    """
    运行语义角色分析
    根据配置选择使用 HanLP 或大模型进行语义角色分析
    
    Args:
        clustered_data: 包含standardized_actions的数据字典
        use_llm: 是否使用大模型进行分析
        api_key: OpenAI API 密钥（如果使用大模型）
        model: 使用的 GPT 模型名称（如果使用大模型）
        save_results: 是否保存结果到文件
        
    Returns:
        dict: 更新后的数据，包含语义角色分析结果
    """
    logging.info("=== 运行语义角色分析 ===")
    # 先复制一份，避免修改原始数据
    clustered_data = json.loads(json.dumps(clustered_data))
    
    # 检查数据结构，处理分组数据或单组数据
    if 'decomposed_function_groups' in clustered_data:
        # 处理分组数据结构
        for group_id, req_group in clustered_data['decomposed_function_groups'].items():
            logging.info(f"处理功能组 {group_id}")
            if 'standardized_actions' in req_group:
                if use_llm and api_key:
                    res = analyze_semantic_roles_llm(req_group, api_key, model=model, save_results=False)
                else:
                    res = analyze_semantic_roles(req_group, save_results=False)
                clustered_data['decomposed_function_groups'][group_id] = res
            else:
                logging.warning(f"功能组 {group_id} 中未找到 standardized_actions 字段")
    else:
        # 处理单组数据结构
        if 'standardized_actions' in clustered_data:
            if use_llm and api_key:
                clustered_data = analyze_semantic_roles_llm(clustered_data, api_key, model=model, save_results=False)
            else:
                clustered_data = analyze_semantic_roles(clustered_data, save_results=False)
        else:
            logging.warning("数据中未找到 standardized_actions 字段")
    
    logging.info("语义角色分析完成")
    
    if save_results:
        # 保存结果到JSON文件
        logging.info("保存语义角色分析结果...")
        os.makedirs(os.path.join(OUTPUT_DIR, "commonReq"), exist_ok=True)
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(OUTPUT_DIR, "commonReq", f"semantic_roles_{timestamp}.json")
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(clustered_data, f, ensure_ascii=False, indent=2)
            logging.info(f"语义角色分析结果已保存到: {output_file}")
        except Exception as e:
            logging.error(f"保存语义角色分析结果失败: {e}")
    else:
        logging.info("...跳过保存语义角色分析结果")
    return clustered_data