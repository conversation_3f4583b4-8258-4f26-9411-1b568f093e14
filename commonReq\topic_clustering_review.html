<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主题聚类结果检阅与调整工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', 'Microsoft YaHei', sans-serif;
            background: #f5f6fa;
            color: #2f3640;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .container {
            max-width: 1400px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .file-input-area {
            border: 2px dashed #ddd;
            padding: 30px;
            text-align: center;
            border-radius: 8px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .file-input-area:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .file-input {
            margin: 10px 0;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background 0.3s ease;
        }

        .btn:hover {
            background: #5a67d8;
        }

        .btn-success {
            background: #48bb78;
        }

        .btn-success:hover {
            background: #38a169;
        }

        .btn-danger {
            background: #e53e3e;
        }

        .btn-danger:hover {
            background: #c53030;
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }

        .topic-groups {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }

        .topic-group {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
            transition: transform 0.2s ease;
        }

        .topic-group:hover {
            transform: translateY(-2px);
        }

        .topic-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .topic-title {
            font-size: 16px;
            font-weight: bold;
            color: #2d3748;
        }

        .topic-count {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .core-function {
            background: #f7fafc;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            border-left: 3px solid #4299e1;
        }

        .core-function-title {
            font-size: 14px;
            font-weight: bold;
            color: #4299e1;
            margin-bottom: 5px;
        }

        .core-function-text {
            font-size: 14px;
            line-height: 1.5;
            color: #4a5568;
            cursor: pointer;
        }

        .core-function-edit {
            width: 100%;
            min-height: 80px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            resize: vertical;
        }

        .requirement-item {
            background: #fafafa;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 10px;
            position: relative;
        }

        .requirement-item.selected {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .requirement-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .requirement-source {
            background: #e2e8f0;
            color: #2d3748;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .requirement-text {
            font-size: 14px;
            line-height: 1.6;
            color: #4a5568;
            word-break: break-all;
        }

        .requirement-checkbox {
            margin-right: 8px;
        }

        .group-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }

        .merge-area {
            background: #fff5f5;
            border: 2px dashed #feb2b2;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            min-height: 100px;
            display: none;
        }

        .merge-area.active {
            display: block;
        }

        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #48bb78;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            display: none;
            z-index: 1000;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 10% auto;
            padding: 30px;
            border: none;
            border-radius: 10px;
            width: 80%;
            max-width: 600px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2d3748;
        }

        .form-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .no-data {
            text-align: center;
            padding: 50px;
            color: #999;
            background: white;
            border-radius: 8px;
        }

        .changes-indicator {
            background: #fed7d7;
            border: 1px solid #feb2b2;
            padding: 10px 15px;
            border-radius: 5px;
            margin-bottom: 15px;
            display: none;
        }

        .changes-indicator.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>主题聚类结果检阅与调整工具</h1>
        <p>专家可以检阅、调整和优化聚类结果</p>
    </div>

    <div class="container">
        <!-- 未保存更改提示 -->
        <div class="changes-indicator" id="changesIndicator">
            ⚠️ 您有未保存的更改，请及时保存或撤销
        </div>

        <!-- 文件加载区域 -->
        <div class="controls">
            <div class="file-input-area">
                <h3>加载聚类结果文件</h3>
                <p>选择 topic_clustering_results.json 或 product_line_variants.json 文件</p>
                <input type="file" id="fileInput" accept=".json" class="file-input">
                <br>
                <button onclick="loadSampleData()" class="btn">加载示例数据</button>
                <button onclick="saveChanges()" class="btn btn-success" id="saveBtn" style="display:none;">💾 保存修改</button>
                <button onclick="discardChanges()" class="btn btn-danger" id="discardBtn" style="display:none;">↶ 撤销修改</button>
                <button onclick="exportResults()" class="btn btn-success">📥 导出结果</button>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="stats" id="statsContainer" style="display: none;">
            <div class="stat-card">
                <div class="stat-number" id="totalRequirements">0</div>
                <div>总需求数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="topicGroups">0</div>
                <div>主题组数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="similarGroups">0</div>
                <div>相似主题组</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="successfulAnalysis">0</div>
                <div>成功分析</div>
            </div>
        </div>

        <!-- 合并区域 -->
        <div class="merge-area" id="mergeArea">
            <h3>已选择 <span id="selectedCount">0</span> 个需求</h3>
            <p>您可以将选中的需求合并到新组或移动到现有组</p>
            <div class="group-actions">
                <button onclick="executeMerge()" class="btn btn-success">🔗 合并到新组</button>
                <button onclick="cancelMerge()" class="btn btn-danger">❌ 取消选择</button>
            </div>
        </div>

        <!-- 主题组显示区域 -->
        <div id="loadingIndicator" class="loading">
            请选择文件加载聚类结果...
        </div>

        <div id="topicGroupsContainer" class="topic-groups" style="display: none;">
        </div>
    </div>

    <!-- 新建主题组模态框 -->
    <div id="newGroupModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2>创建新主题组</h2>
            <div class="form-group">
                <label class="form-label" for="newGroupFunction">核心功能描述:</label>
                <textarea id="newGroupFunction" class="form-input" rows="3" placeholder="请输入核心功能描述..."></textarea>
            </div>
            <div class="group-actions">
                <button onclick="createNewGroup()" class="btn btn-success">✅ 创建</button>
                <button onclick="closeModal()" class="btn">❌ 取消</button>
            </div>
        </div>
    </div>

    <!-- 提示消息 -->
    <div id="toast" class="toast"></div>

    <script>
        let clusteringData = null;
        let originalData = null;
        let selectedRequirements = [];
        let hasUnsavedChanges = false;

        // 页面卸载前提醒保存
        window.addEventListener('beforeunload', function(e) {
            if (hasUnsavedChanges) {
                e.preventDefault();
                e.returnValue = '您有未保存的更改，确定要离开吗？';
            }
        });

        // 文件加载处理
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                loadFile(file);
            }
        });

        function loadFile(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    clusteringData = JSON.parse(e.target.result);
                    originalData = JSON.parse(JSON.stringify(clusteringData)); // 深拷贝保存原始数据
                    hasUnsavedChanges = false;
                    updateSaveButtons();
                    renderClusteringResults();
                    showToast('文件加载成功！');
                } catch (error) {
                    showToast('JSON 文件格式错误: ' + error.message, 'error');
                }
            };
            reader.readAsText(file);
        }

        function loadSampleData() {
            // 尝试加载同目录下的示例文件
            const sampleUrl = 'topic_clustering_results.json';
            
            fetch(sampleUrl)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`无法找到示例文件 ${sampleUrl}`);
                    }
                    return response.json();
                })
                .then(data => {
                    clusteringData = data;
                    originalData = JSON.parse(JSON.stringify(data));
                    hasUnsavedChanges = false;
                    updateSaveButtons();
                    renderClusteringResults();
                    showToast('示例数据加载成功！');
                })
                .catch(error => {
                    console.error('加载示例数据失败:', error);
                    showToast('无法加载示例数据，请手动选择JSON文件', 'error');
                });
        }

        function renderClusteringResults() {
            if (!clusteringData) return;

            document.getElementById('loadingIndicator').style.display = 'none';
            document.getElementById('statsContainer').style.display = 'grid';
            document.getElementById('topicGroupsContainer').style.display = 'grid';

            // 更新统计信息
            updateStats();

            // 渲染主题组
            const container = document.getElementById('topicGroupsContainer');
            container.innerHTML = '';

            const topicGroups = clusteringData.similar_topic_groups || clusteringData.variant_groups || {};
            
            Object.entries(topicGroups).forEach(([groupId, groupData]) => {
                const groupElement = createTopicGroupElement(groupId, groupData);
                container.appendChild(groupElement);
            });

            // 添加创建新组的按钮
            const newGroupButton = document.createElement('div');
            newGroupButton.className = 'topic-group';
            newGroupButton.style.borderStyle = 'dashed';
            newGroupButton.style.borderColor = '#667eea';
            newGroupButton.style.cursor = 'pointer';
            newGroupButton.innerHTML = `
                <div style="text-align: center; color: #667eea; font-size: 48px; margin-bottom: 10px;">+</div>
                <div style="text-align: center; color: #667eea; font-weight: bold;">创建新主题组</div>
            `;
            newGroupButton.onclick = () => openNewGroupModal();
            container.appendChild(newGroupButton);
        }

        function updateStats() {
            const metadata = clusteringData.metadata || {};
            const topicGroups = clusteringData.similar_topic_groups || clusteringData.variant_groups || {};
            const totalRequirements = Object.values(topicGroups).reduce((total, group) => 
                total + (group.requirements ? group.requirements.length : 0), 0);

            document.getElementById('totalRequirements').textContent = totalRequirements;
            document.getElementById('topicGroups').textContent = Object.keys(topicGroups).length;
            document.getElementById('similarGroups').textContent = Object.keys(topicGroups).length;
            document.getElementById('successfulAnalysis').textContent = metadata.successful_analysis || totalRequirements;
        }

        function createTopicGroupElement(groupId, groupData) {
            const groupDiv = document.createElement('div');
            groupDiv.className = 'topic-group';
            groupDiv.setAttribute('data-group-id', groupId);

            const requirements = groupData.requirements || [];
            const coreFunction = groupData.core_function || '';
            const variabilityPoints = groupData.variability_points || [];

            groupDiv.innerHTML = `
                <div class="topic-header">
                    <span class="topic-title">主题组 ${groupId}</span>
                    <span class="topic-count">${requirements.length} 个需求</span>
                </div>
                <div class="core-function">
                    <div class="core-function-title">核心功能</div>
                    <div class="core-function-text" onclick="editCoreFunction(this, '${groupId}')">${coreFunction || '点击编辑核心功能描述...'}</div>
                </div>
                ${variabilityPoints.length > 0 ? `
                <div class="core-function">
                    <div class="core-function-title">变异点</div>
                    <div class="core-function-text">${variabilityPoints.join(', ')}</div>
                </div>
                ` : ''}
                <div class="requirements-list">
                    ${requirements.map((req, index) => createRequirementElement(req, groupId, index)).join('')}
                </div>
                <div class="group-actions">
                    <button onclick="selectAllRequirements('${groupId}')" class="btn">📋 全选</button>
                    <button onclick="moveSelectedToGroup('${groupId}')" class="btn">➡️ 移入选中</button>
                    <button onclick="splitGroup('${groupId}')" class="btn">✂️ 拆分组</button>
                    <button onclick="deleteGroup('${groupId}')" class="btn btn-danger">🗑️ 删除组</button>
                </div>
            `;

            return groupDiv;
        }

        function createRequirementElement(req, groupId, index) {
            const reqId = `${groupId}_${index}`;
            const text = req.req_content || req.text || '';
            const source = req.source || '未知来源';
            
            return `
                <div class="requirement-item" data-req-id="${reqId}" data-group-id="${groupId}" data-req-index="${index}">
                    <div class="requirement-header">
                        <label>
                            <input type="checkbox" class="requirement-checkbox" onchange="toggleRequirementSelection('${reqId}')">
                            <span class="requirement-source">${source}</span>
                        </label>
                    </div>
                    <div class="requirement-text">${text.substring(0, 200)}${text.length > 200 ? '...' : ''}</div>
                </div>
            `;
        }

        function editCoreFunction(element, groupId) {
            const currentText = element.textContent;
            const textarea = document.createElement('textarea');
            textarea.className = 'core-function-edit';
            textarea.value = currentText === '点击编辑核心功能描述...' ? '' : currentText;
            
            textarea.onblur = function() {
                const newText = textarea.value.trim() || '点击编辑核心功能描述...';
                element.textContent = newText;
                element.style.display = 'block';
                textarea.remove();
                
                // 更新数据
                if (clusteringData.similar_topic_groups && clusteringData.similar_topic_groups[groupId]) {
                    clusteringData.similar_topic_groups[groupId].core_function = newText;
                } else if (clusteringData.variant_groups && clusteringData.variant_groups[groupId]) {
                    clusteringData.variant_groups[groupId].core_function = newText;
                }
                
                markAsChanged();
                showToast('核心功能已更新');
            };

            textarea.onkeydown = function(e) {
                if (e.key === 'Enter' && e.ctrlKey) {
                    textarea.blur();
                }
                if (e.key === 'Escape') {
                    element.style.display = 'block';
                    textarea.remove();
                }
            };

            element.style.display = 'none';
            element.parentNode.appendChild(textarea);
            textarea.focus();
        }

        function toggleRequirementSelection(reqId) {
            const checkbox = document.querySelector(`[data-req-id="${reqId}"] .requirement-checkbox`);
            const item = document.querySelector(`[data-req-id="${reqId}"]`);
            
            if (checkbox.checked) {
                selectedRequirements.push(reqId);
                item.classList.add('selected');
            } else {
                selectedRequirements = selectedRequirements.filter(id => id !== reqId);
                item.classList.remove('selected');
            }

            updateMergeArea();
        }

        function updateMergeArea() {
            const mergeArea = document.getElementById('mergeArea');
            const selectedCount = document.getElementById('selectedCount');
            
            if (selectedRequirements.length > 0) {
                mergeArea.classList.add('active');
                selectedCount.textContent = selectedRequirements.length;
            } else {
                mergeArea.classList.remove('active');
            }
        }

        function selectAllRequirements(groupId) {
            const checkboxes = document.querySelectorAll(`[data-group-id="${groupId}"] .requirement-checkbox`);
            checkboxes.forEach(checkbox => {
                if (!checkbox.checked) {
                    checkbox.checked = true;
                    toggleRequirementSelection(checkbox.closest('.requirement-item').getAttribute('data-req-id'));
                }
            });
        }

        function moveSelectedToGroup(targetGroupId) {
            if (selectedRequirements.length === 0) {
                showToast('请先选择要移动的需求', 'error');
                return;
            }

            if (!confirm(`确定要将 ${selectedRequirements.length} 个需求移动到主题组 ${targetGroupId} 吗？`)) {
                return;
            }

            const movedRequirements = [];
            const sourceGroups = new Set();

            // 收集要移动的需求数据（按索引降序排序避免删除时索引错位）
            const reqsToMove = selectedRequirements.map(reqId => {
                const reqElement = document.querySelector(`[data-req-id="${reqId}"]`);
                const sourceGroupId = reqElement.getAttribute('data-group-id');
                const reqIndex = parseInt(reqElement.getAttribute('data-req-index'));
                sourceGroups.add(sourceGroupId);

                const sourceGroup = getGroupData(sourceGroupId);
                if (sourceGroup && sourceGroup.requirements[reqIndex]) {
                    return {
                        requirement: sourceGroup.requirements[reqIndex],
                        sourceGroupId: sourceGroupId,
                        reqIndex: reqIndex
                    };
                }
                return null;
            }).filter(item => item !== null).sort((a, b) => b.reqIndex - a.reqIndex);

            // 执行移动操作
            reqsToMove.forEach(item => {
                const sourceGroup = getGroupData(item.sourceGroupId);
                const targetGroup = getGroupData(targetGroupId);
                
                if (sourceGroup && targetGroup && item.sourceGroupId !== targetGroupId) {
                    // 从源组移除
                    sourceGroup.requirements.splice(item.reqIndex, 1);
                    // 添加到目标组
                    targetGroup.requirements.push(item.requirement);
                    movedRequirements.push(item);
                }
            });

            if (movedRequirements.length > 0) {
                updateMetadata();
                clearSelection();
                renderClusteringResults();
                
                showToast(`成功移动 ${movedRequirements.length} 个需求到主题组 ${targetGroupId}`);
                
                // 高亮显示目标组
                setTimeout(() => {
                    const targetElement = document.querySelector(`[data-group-id="${targetGroupId}"]`);
                    if (targetElement) {
                        targetElement.style.border = '3px solid #48bb78';
                        targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        setTimeout(() => {
                            targetElement.style.border = '';
                        }, 2000);
                    }
                }, 100);
            } else {
                showToast('没有需求被移动（可能选中的需求已在目标组中）', 'info');
            }
        }

        function splitGroup(groupId) {
            if (selectedRequirements.length === 0) {
                showToast('请先选择要拆分的需求', 'error');
                return;
            }

            // 检查选中的需求是否都来自这个组
            const validRequirements = selectedRequirements.filter(reqId => {
                const element = document.querySelector(`[data-req-id="${reqId}"]`);
                return element && element.getAttribute('data-group-id') === groupId;
            });

            if (validRequirements.length === 0) {
                showToast('没有选中来自此组的需求', 'error');
                return;
            }

            openNewGroupModal();
        }

        function deleteGroup(groupId) {
            const group = getGroupData(groupId);
            const reqCount = group ? (group.requirements ? group.requirements.length : 0) : 0;
            
            if (confirm(`确定要删除主题组 ${groupId} 吗？这将删除组内的 ${reqCount} 个需求。`)) {
                if (clusteringData.similar_topic_groups && clusteringData.similar_topic_groups[groupId]) {
                    delete clusteringData.similar_topic_groups[groupId];
                } else if (clusteringData.variant_groups && clusteringData.variant_groups[groupId]) {
                    delete clusteringData.variant_groups[groupId];
                }
                
                updateMetadata();
                clearSelection();
                renderClusteringResults();
                showToast('主题组已删除');
            }
        }

        function executeMerge() {
            if (selectedRequirements.length < 1) {
                showToast('请先选择要合并的需求', 'error');
                return;
            }
            openNewGroupModal();
        }

        function cancelMerge() {
            clearSelection();
        }

        function clearSelection() {
            selectedRequirements = [];
            document.querySelectorAll('.requirement-checkbox').forEach(cb => cb.checked = false);
            document.querySelectorAll('.requirement-item').forEach(item => item.classList.remove('selected'));
            updateMergeArea();
        }

        function openNewGroupModal() {
            document.getElementById('newGroupModal').style.display = 'block';
            document.getElementById('newGroupFunction').focus();
        }

        function closeModal() {
            document.getElementById('newGroupModal').style.display = 'none';
            document.getElementById('newGroupFunction').value = '';
        }

        function createNewGroup() {
            const coreFunction = document.getElementById('newGroupFunction').value.trim();
            if (!coreFunction) {
                showToast('请输入核心功能描述', 'error');
                return;
            }

            if (selectedRequirements.length === 0) {
                showToast('请先选择要加入新组的需求', 'error');
                return;
            }

            // 生成新的组ID
            const existingIds = Object.keys(clusteringData.similar_topic_groups || clusteringData.variant_groups || {});
            const newId = Math.max(...existingIds.map(id => parseInt(id)), 0) + 1;

            // 收集选中的需求并从原组中移除
            const requirements = [];
            const reqsToRemove = selectedRequirements.map(reqId => {
                const reqElement = document.querySelector(`[data-req-id="${reqId}"]`);
                const sourceGroupId = reqElement.getAttribute('data-group-id');
                const reqIndex = parseInt(reqElement.getAttribute('data-req-index'));
                
                const sourceGroup = getGroupData(sourceGroupId);
                if (sourceGroup && sourceGroup.requirements[reqIndex]) {
                    requirements.push(sourceGroup.requirements[reqIndex]);
                    return { sourceGroup, reqIndex };
                }
                return null;
            }).filter(item => item !== null).sort((a, b) => b.reqIndex - a.reqIndex);

            // 从原组中移除需求
            reqsToRemove.forEach(item => {
                item.sourceGroup.requirements.splice(item.reqIndex, 1);
            });

            // 创建新组
            const newGroup = {
                core_function: coreFunction,
                requirements: requirements,
                variability_points: [],
                commonalities: []
            };

            // 添加到数据中
            const targetContainer = clusteringData.similar_topic_groups || clusteringData.variant_groups;
            if (targetContainer) {
                targetContainer[newId] = newGroup;
            } else {
                clusteringData.similar_topic_groups = { [newId]: newGroup };
            }

            updateMetadata();
            closeModal();
            clearSelection();
            renderClusteringResults();
            showToast('新主题组创建成功');

            // 高亮新组
            setTimeout(() => {
                const newElement = document.querySelector(`[data-group-id="${newId}"]`);
                if (newElement) {
                    newElement.style.border = '3px solid #48bb78';
                    newElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    setTimeout(() => {
                        newElement.style.border = '';
                    }, 2000);
                }
            }, 100);
        }

        function getGroupData(groupId) {
            return (clusteringData.similar_topic_groups && clusteringData.similar_topic_groups[groupId]) ||
                   (clusteringData.variant_groups && clusteringData.variant_groups[groupId]) ||
                   (clusteringData.all_topic_groups && clusteringData.all_topic_groups[groupId]);
        }

        function markAsChanged() {
            hasUnsavedChanges = true;
            updateSaveButtons();
        }

        function updateSaveButtons() {
            const saveBtn = document.getElementById('saveBtn');
            const discardBtn = document.getElementById('discardBtn');
            const changesIndicator = document.getElementById('changesIndicator');
            
            if (hasUnsavedChanges) {
                saveBtn.style.display = 'inline-block';
                discardBtn.style.display = 'inline-block';
                changesIndicator.classList.add('active');
                if (!document.title.includes('*')) {
                    document.title = '* ' + document.title;
                }
            } else {
                saveBtn.style.display = 'none';
                discardBtn.style.display = 'none';
                changesIndicator.classList.remove('active');
                document.title = document.title.replace('* ', '');
            }
        }

        function saveChanges() {
            if (!hasUnsavedChanges) {
                showToast('没有需要保存的更改', 'info');
                return;
            }

            // 更新原始数据
            originalData = JSON.parse(JSON.stringify(clusteringData));
            hasUnsavedChanges = false;
            updateSaveButtons();
            showToast('更改已保存到内存，可以导出结果');
        }

        function discardChanges() {
            if (!hasUnsavedChanges) {
                showToast('没有需要撤销的更改', 'info');
                return;
            }

            if (confirm('确定要撤销所有未保存的更改吗？')) {
                // 恢复原始数据
                clusteringData = JSON.parse(JSON.stringify(originalData));
                hasUnsavedChanges = false;
                updateSaveButtons();
                clearSelection();
                renderClusteringResults();
                showToast('已撤销所有更改');
            }
        }

        function updateMetadata() {
            if (!clusteringData.metadata) {
                clusteringData.metadata = {};
            }
            
            // 更新统计信息
            const topicGroups = clusteringData.similar_topic_groups || clusteringData.variant_groups || {};
            const totalRequirements = Object.values(topicGroups).reduce((total, group) => 
                total + (group.requirements ? group.requirements.length : 0), 0);
            
            clusteringData.metadata.topic_groups = Object.keys(topicGroups).length;
            clusteringData.metadata.similar_topic_groups = Object.keys(topicGroups).length;
            clusteringData.metadata.total_requirements = totalRequirements;
            clusteringData.metadata.last_modified = new Date().toLocaleString('zh-CN');
            
            markAsChanged();
            updateStats();
        }

        function exportResults() {
            if (!clusteringData) {
                showToast('没有数据可导出', 'error');
                return;
            }

            // 更新时间戳
            if (clusteringData.metadata) {
                clusteringData.metadata.timestamp = new Date().toLocaleString('zh-CN');
                clusteringData.metadata.modified_by = 'expert_review_tool';
            }

            const dataStr = JSON.stringify(clusteringData, null, 2);
            const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
            
            const exportFileDefaultName = 'topic_clustering_results_reviewed.json';
            
            const linkElement = document.createElement('a');
            linkElement.setAttribute('href', dataUri);
            linkElement.setAttribute('download', exportFileDefaultName);
            linkElement.click();
            
            showToast('结果已导出');
        }

        function showToast(message, type = 'success') {
            const toast = document.getElementById('toast');
            toast.textContent = message;
            toast.style.display = 'block';
            
            if (type === 'error') {
                toast.style.background = '#e53e3e';
            } else if (type === 'info') {
                toast.style.background = '#3182ce';
            } else {
                toast.style.background = '#48bb78';
            }
            
            setTimeout(() => {
                toast.style.display = 'none';
            }, 3000);
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('newGroupModal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                if (hasUnsavedChanges) {
                    saveChanges();
                }
            }
            if (e.ctrlKey && e.key === 'z') {
                e.preventDefault();
                if (hasUnsavedChanges) {
                    discardChanges();
                }
            }
        });
    </script>
</body>
</html>
