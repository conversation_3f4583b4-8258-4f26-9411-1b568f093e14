# Cluster and Standardize Actions 模块设计文档

## 概述

`cluster_and_standardize_actions.py` 模块提供了两阶段的动作处理流程：第一阶段对处理步骤中的动作进行聚类，第二阶段对聚类结果进行标准化。该模块专门用于识别和归类需求分解后的相似动作，并生成标准化的动作描述，为后续的可变性分析奠定基础。

## 功能特性

### 1. 两阶段处理架构
- **第一阶段**: 动作聚类 - 识别相似的处理步骤
- **第二阶段**: 动作标准化 - 生成统一的动作描述
- 支持阶段间结果检查和调整

### 2. 智能动作识别
- 基于 LLM 的语义理解
- 支持上位词/下位词关系识别
- 考虑业务领域的专业术语

### 3. 优化的处理策略
- 按需求复杂度排序处理
- 优先处理简单需求建立基础类别
- 渐进式聚类算法

## 核心函数设计

### 1. 第一阶段：动作聚类

#### `cluster_processing_steps(requirement_group_data, api_key, ...)`

**功能**: 对需求组中的 processing_steps 进行动作聚类

**核心参数**:
- `requirement_group_data`: 包含 decomposed_requirements 的需求组数据
- `api_key`: OpenAI API 密钥
- `model`: 使用的 GPT 模型（默认 gpt-4-turbo）
- `similarity_threshold`: 动作相似度阈值（默认 0.8）

**处理流程**:

1. **步骤收集与排序**:
```python
# 提取所有 processing_steps
all_steps = []
for req in decomposed_reqs:
    for step in req["processing_steps"]:
        all_steps.append({
            "step": step,
            "req_id": req_id,
            "req_step_count": len(steps)  # 用于排序
        })

# 按步骤数量排序：优先处理简单需求
all_steps.sort(key=lambda x: (x["req_step_count"], x["original_index"]))
```

2. **LLM 动作聚类分析**:
```python
def _call_llm_for_action_clustering(target_step: str, existing_clusters: Dict) -> Dict:
    system_prompt = """你是一个专业的软件需求分析师，专门识别和聚类软件处理步骤中的核心动作。

    核心动作等价的判断标准：
    1. 核心谓词相同或语义极其相近（如"建立"与"创建"、"监控"与"检测"）
    2. 动作的对象类型相同或相似
    3. 动作的目的和效果基本一致
    4. 上位词关系：如果某个动作是另一个动作的上位词或下位词，应该归为一类

    输出格式（严格JSON）：
    {
        "is_same_action": true/false,
        "matched_cluster_id": 匹配的类别ID,
        "core_action_description": "核心动作的简洁描述"
    }"""
```

3. **渐进式聚类构建**:
- 逐步分析每个处理步骤
- 判断是否与现有类别匹配
- 创建新类别或归入现有类别
- 维护完整的溯源信息

**优化特性**:
- **智能排序**: 优先处理来自步骤少的需求，建立稳定的基础类别
- **上位词识别**: 识别 "数据采集" 与 "飞行数据采集" 的包含关系
- **业务语义**: 保持业务领域的专业术语准确性

### 2. 第二阶段：动作标准化

#### `standardize_action_clusters(clustered_data, api_key, ...)`

**功能**: 对第一阶段的聚类结果进行标准化总结

**核心流程**:

1. **标准化 LLM 分析**:
```python
def _call_llm_for_standardization(cluster_steps: List[Dict]) -> Dict:
    system_prompt = """你是一个专业的软件需求分析师，专门对相似的处理步骤进行标准化总结。

    标准化要求：
    1. 保留业务领域的专业术语和关键对象
    2. 提取核心动作，但结合具体业务对象
    3. 保持足够的业务特异性，避免过于通用的描述
    4. 简洁明了，但能准确反映业务含义
    5. 保持一致的格式风格：业务对象 + 动作

    业务相关的标准化例子：
    - "飞行数据采集"（而不是"数据处理"）
    - "飞行调整指令计算"（而不是"指令执行"）

    输出格式（严格JSON）：
    {
        "standardized_action": "标准化的动作名称（业务特定且简洁）"
    }"""
```

2. **溯源信息维护**:
```python
standardized_actions[standardized_action] = {
    "steps_with_source": [
        {
            "step": step['step'],
            "req_id": step['req_id'],
            "original_index": step['original_index']
        } for step in steps
    ],
    "req_sources": list(set([step['req_id'] for step in steps])),
    "step_count": len(steps),
    "cluster_id": cluster_id
}
```

### 3. 完整流程函数

#### `cluster_and_standardize_actions(decomposed_req, api_key, ...)`

**功能**: 完整的两阶段动作聚类和标准化流程

**特点**:
- 自动化的端到端处理
- 支持分组数据结构处理
- 完整的结果追踪和保存

**处理架构**:
```python
# 处理每个功能组
for group_id, req_group in decomposed_req['decomposed_function_groups'].items():
    # 第一阶段：聚类
    clustered_result = cluster_processing_steps(req_group, ...)
    
    # 第二阶段：标准化
    final_result = standardize_action_clusters(clustered_result, ...)
    
    # 保存结果
    decomposed_req['decomposed_function_groups'][group_id] = final_result
```

## 数据结构设计

### 输入数据结构
```python
{
    "decomposed_function_groups": {
        "group_1": {
            "core_function": "无人机飞行控制",
            "decomposed_requirements": [
                {
                    "req_id": "需求_1",
                    "processing_steps": [
                        "系统实时采集飞行姿态数据",
                        "系统通过算法计算调整指令",
                        "系统驱动执行机构"
                    ]
                }
            ]
        }
    }
}
```

### 第一阶段输出（动作聚类）
```python
{
    "action_clusters": {
        1: {
            "steps": [
                {
                    "step": "系统实时采集飞行姿态数据",
                    "req_id": "需求_1",
                    "original_index": 0,
                    "req_step_count": 3
                }
            ],
            "temp_description": "数据采集"
        }
    },
    "clustering_metadata": {
        "total_steps": 10,
        "action_clusters": 3,
        "failed_steps": 0,
        "similarity_threshold": 0.8
    }
}
```

### 第二阶段输出（动作标准化）
```python
{
    "standardized_actions": {
        "飞行数据采集": {
            "steps_with_source": [
                {
                    "step": "系统实时采集飞行姿态数据",
                    "req_id": "需求_1", 
                    "original_index": 0
                }
            ],
            "req_sources": ["需求_1", "需求_2"],
            "step_count": 2,
            "cluster_id": 1
        }
    },
    "standardization_metadata": {
        "successful_standardizations": 3,
        "failed_standardizations": 0
    }
}
```

## 核心算法特性

### 1. 智能排序策略
```python
# 按需求复杂度排序：优先处理步骤少的需求
all_steps.sort(key=lambda x: (x["req_step_count"], x["original_index"]))
```

**优势**:
- 简单需求建立稳定的基础类别
- 复杂需求后续匹配更准确
- 减少聚类的不确定性

### 2. 上位词识别机制
```python
# 系统Prompt中的上位词识别规则
上位词关系：如果某个动作是另一个动作的上位词或下位词，它们应该归为同一类
例如："数据采集"（上位词）与"飞行数据采集"（下位词）应该归为一类
例如："控制执行"（上位词）与"执行机构控制"（下位词）应该归为一类
```

### 3. 业务特异性保持
```python
# 标准化时保持业务特异性
标准化要求：
1. 保留业务领域的专业术语和关键对象
2. 提取核心动作，但结合具体业务对象  
3. 保持足够的业务特异性，避免过于通用的描述

正确示例：
- "飞行数据采集"（业务特定）
- "飞行调整指令计算"（业务特定）

错误示例：
- "数据处理"（过于通用）
- "指令执行"（过于通用）
```

## 性能优化策略

### 1. API 调用优化
```python
# 速率控制
time.sleep(0.6)  # 避免 API 限制

# 重试机制
for attempt in range(max_retries):
    if "rate limit" in error_msg:
        wait_time = delay * (2 ** attempt)  # 指数退避
```

### 2. 内存管理
- 流式处理避免内存溢出
- 及时释放不需要的中间数据
- 分批处理大量步骤

### 3. 结果缓存
- 中间结果自动保存
- 支持断点续传
- 避免重复处理

## 错误处理机制

### API 调用错误
```python
# 多层错误处理
try:
    response = client.chat.completions.create(...)
except Exception as e:
    if "rate limit" in error_msg:
        # 速率限制处理
    elif "timeout" in error_msg:
        # 超时处理  
    else:
        # 通用错误处理
```

### 数据验证
- JSON 格式验证
- 必需字段检查
- 数据类型验证
- 业务逻辑验证

### 容错机制
- 单个处理失败不影响整体
- 详细的错误日志记录
- 失败步骤的完整追踪

## 演示功能

### 1. 阶段演示函数
```python
def demo_stage_one_clustering():
    """演示第一阶段：动作聚类"""
    
def demo_stage_two_standardization():  
    """演示第二阶段：动作标准化"""
    
def demo_improved_clustering():
    """演示改进后的聚类效果"""
```

### 2. 改进展示
- 排序前后对比
- 上位词识别效果
- 处理速度提升
- 分类准确性改善

## 配置依赖

### 外部配置
```python
from commonReq.config import BASE_URL, OUTPUT_DIR, logging
```

### Python 依赖
```python
# API 交互
openai                 # OpenAI API 客户端

# 数据处理
typing                 # 类型提示
collections           # 数据结构工具

# 标准库
json, os, re, time
```

## 使用示例

### 完整流程
```python
# 初始化并运行完整流程
result = cluster_and_standardize_actions(
    decomposed_req=decomposed_data,
    api_key=api_key,
    model="gpt-4-turbo",
    similarity_threshold=0.8,
    save_results=True
)
```

### 分阶段处理
```python
# 第一阶段：动作聚类
clustered_result = cluster_processing_steps(
    requirement_group_data=group_data,
    api_key=api_key,
    similarity_threshold=0.8
)

# 检查中间结果...

# 第二阶段：动作标准化  
final_result = standardize_action_clusters(
    clustered_data=clustered_result,
    api_key=api_key
)
```

## 输出文件格式

### 中间结果文件
- **第一阶段**: `action_clusters_{safe_filename}.json`
- **第二阶段**: `standardized_actions_{safe_filename}.json`

### 最终结果文件
- **完整流程**: `cluster_and_standardized_reqs_{timestamp}.json`

### 文件内容示例
```json
{
    "decomposed_function_groups": {
        "1": {
            "action_clusters": {...},
            "standardized_actions": {
                "飞行数据采集": {
                    "steps_with_source": [...],
                    "req_sources": ["需求_1", "需求_2"],
                    "step_count": 3
                }
            },
            "clustering_metadata": {...},
            "standardization_metadata": {...}
        }
    }
}
```

## 质量保证

### 聚类质量指标
- 类内相似性
- 类间差异性  
- 业务语义一致性

### 标准化质量指标
- 描述准确性
- 业务特异性保持
- 术语一致性

## 扩展建议

### 功能扩展
1. **交互式调整**: 支持用户对聚类结果进行手工调整
2. **质量评估**: 添加聚类和标准化质量的自动评估
3. **增量学习**: 支持基于历史结果的模型优化
4. **多领域适配**: 支持不同业务领域的定制化处理

### 性能改进
1. **并行处理**: 利用多线程/多进程加速处理
2. **缓存优化**: 实现智能缓存机制
3. **批处理优化**: 支持更大批量的并行处理
4. **GPU 加速**: 利用 GPU 加速 LLM 推理

### 工具集成
1. **可视化界面**: 提供聚类结果的可视化展示
2. **编辑工具**: 开发交互式的结果编辑界面
3. **API 封装**: 提供标准的 RESTful API 接口
4. **流程编排**: 集成到更大的需求分析流水线中
