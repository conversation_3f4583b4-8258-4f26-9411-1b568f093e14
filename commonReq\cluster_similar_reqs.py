import json
import os
import re
from typing import List, Dict, Any
import hanlp
import pandas as pd
from sentence_transformers import SentenceTransformer
from umap import UMAP
from hdbscan import HDBSCAN
from sklearn.cluster import KMeans
from sklearn.feature_extraction.text import CountVectorizer
from bertopic.representation import KeyBERTInspired
from bertopic import BERTopic
from sentence_transformers import SentenceTransformer
from collections import defaultdict
from openai import OpenAI
import time
import numpy as np
from commonReq.config import BASE_URL,MODEL_DIR,DOCS_DIR, OUTPUT_DIR,logging,EMBEDDING_MODEL,SRL_MODEL,COMMONREQ_DATA

save_to = os.path.join(MODEL_DIR, "bertopic")

# 那些在语义角色标注中常见的角色
SEMANTIC_ROLES = [
    "ARG0", "ARG1", "ARG2", "ARG3", "ARG4",
    "ARGM-CND", "ARGM-LOC", "ARGM-MNR","ARGM-TMP"
]

try:
    stopwords_path = os.path.join(DOCS_DIR, "stopwords.txt")
    with open(stopwords_path, "r", encoding="utf-8") as f:
        stopwords = f.read().split("\n")
except FileNotFoundError:
    print(f"File {stopwords_path} not found.")
    stopwords = "english"


def convert_numpy_types(obj):
    """
    递归转换对象中的numpy类型为Python原生类型，确保JSON序列化兼容性
    """
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {convert_numpy_types(k): convert_numpy_types(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    elif isinstance(obj, tuple):
        return tuple(convert_numpy_types(item) for item in obj)
    else:
        return obj


def load_requirement_dict(re_dirs: str = COMMONREQ_DATA) -> List[Dict[str, str]]:
    """
    从目录下的多系统 jsonl 文件加载需求摘要。
    每个 jsonl 文件代表一个系统，文件名为系统名。
    返回每个需求的文本和来源系统名。
    """
    abstracts = []
    for filename in os.listdir(re_dirs):
        if not filename.endswith(".jsonl"):
            continue
        system_name = os.path.splitext(filename)[0]
        file_path = os.path.join(re_dirs, filename)
        with open(file_path, "r", encoding="utf-8") as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                try:
                    obj = json.loads(line)
                except json.JSONDecodeError:
                    continue
                req_id = obj.get("req_id", "")
                req_name = obj.get("req_name", "")
                content = obj.get("req_content", "")
                if not content:
                    continue
                abstracts.append({
                    "req_id" : req_id,
                    "req_content": content,
                    "req_name" : req_name,
                    "source": system_name
                })
    return abstracts


def cluster_similar_requirements(re_dirs, save_model=False, save_results=True):
    """
    第一阶段：聚类相似需求
    使用 BERTopic 对需求摘要进行主题聚类
    
    Args:
        save_model (bool): 是否保存训练的 BERTopic 模型
        save_results (bool): 是否保存聚类结果为 JSON 格式
    
    Returns:
        tuple: (topic_model, topics_dict, abstracts)
            - topic_model: 训练好的 BERTopic 模型
            - topics_dict: 按主题分组的文档字典
            - abstracts: 原始摘要列表
    """
    logging.info("=== 第一阶段：聚类相似需求 ===")
    
    # Embedding model
    embedding_model = SentenceTransformer(EMBEDDING_MODEL)

    # Load req abstract
    abstracts = load_requirement_dict(re_dirs)
    logging.info(f"加载了 {len(abstracts)} 个需求摘要")

    # 提取文本内容供 BERTopic 使用
    texts = [req['req_content'] for req in abstracts]
    
    # Reduce dimensionality
    umap_model = UMAP(n_neighbors=10, n_components=5, min_dist=0.1, metric='cosine', random_state=42)

    # Cluster
    cluster_model = HDBSCAN(min_cluster_size=3, metric='euclidean', cluster_selection_method='eom', prediction_data=True)
    # cluster_model = KMeans(n_clusters=10)  # 如需使用KMeans，需要特殊处理

    # Vectorizer
    vectorizer_model = CountVectorizer(stop_words=stopwords,min_df=0.05,max_df=0.9,ngram_range=(1, 2))

    # KeyBERT as representation_model
    keybert_model = KeyBERTInspired()

    # Create model
    try:
        topic_model = BERTopic.load(save_to, embedding_model=embedding_model)
        logging.info("成功加载已保存的模型")
    except:
        logging.info("加载模型失败，开始训练新模型...")
        topic_model = BERTopic(
            # Pipeline models
            hdbscan_model=cluster_model,  # 使用HDBSCAN进行聚类
            embedding_model=embedding_model,
            umap_model=umap_model,
            vectorizer_model=vectorizer_model,
            representation_model=keybert_model,

            # Hyperparameters
            top_n_words=5,
            min_topic_size=4,
            nr_topics="auto",  # 使用自动确定主题数量，与HDBSCAN一致
            verbose=True
        )

        # Train model
        logging.info("开始训练主题模型...")
        topic_model.fit(texts)  # 使用文本列表而不是字典列表
        logging.info("主题模型训练完成")

    # Transform abstracts to topics
    topics, _ = topic_model.transform(texts)  # 使用文本列表而不是字典列表

    # 创建一个字典来存储主题及其文档
    topics_dict = defaultdict(list)

    # 遍历所有文档和对应的主题
    for doc, topic in zip(abstracts, topics):
        topics_dict[topic].append(doc)

    # 输出聚类结果摘要
    logging.info(f"聚类完成，共发现 {len(topics_dict)} 个主题:")
    for topic_num, topic_docs in topics_dict.items():
        if topic_num == -1:
            logging.info(f"  主题 {topic_num} (未归类): {len(topic_docs)} 个文档")
        else:
            # 获取主题的关键词
            topic_keywords = topic_model.get_topic(topic_num)
            if topic_keywords and isinstance(topic_keywords, list):
                keywords = ", ".join([word for word, _ in topic_keywords[:3]])  # 只显示前3个关键词
            else:
                keywords = f"主题 {topic_num}"
            logging.info(f"  主题 {topic_num} ({keywords}): {len(topic_docs)} 个文档")

    # 保存主题模型
    if save_model:
        try:
            logging.info(f"保存模型到 {save_to}")
            topic_model.save(save_to, serialization="pytorch")
        except Exception as e:
            logging.error(f"保存 BERTopic 模型失败: {e}")

    # 保存聚类结果
    if save_results:
        output_file = os.path.join(OUTPUT_DIR, "commonReq", "topic_clustering_results.json")
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # 构造与 cluster_similar_requirements_llm 一致的数据格式
        clustered_topics = {}
        standalone_topics = {}
        failed_requirements = []  # BERTopic 通常不会有失败的需求
        
        # 转换 topics_dict 为与 LLM 版本一致的格式
        for topic_num, topic_docs in topics_dict.items():
            # 确保 topic_num 是 Python 原生的 int 类型，避免 numpy int32 导致的 JSON 序列化错误
            topic_num_int = int(topic_num)
            
            if topic_num_int == -1:
                # 未归类的文档
                for i, doc in enumerate(topic_docs):
                    standalone_topics[f"standalone_{i}"] = {
                        'text': doc['req_content'] if isinstance(doc, dict) else str(doc),
                        'core_function': "未归类需求",
                        'reason': "BERTopic 未能归类到任何主题"
                    }
            else:
                # 获取主题的关键词作为核心功能描述
                topic_keywords = topic_model.get_topic(topic_num)
                if topic_keywords and isinstance(topic_keywords, list):
                    core_function = ", ".join([word for word, _ in topic_keywords[:5]])  # 前5个关键词
                else:
                    core_function = f"主题 {topic_num_int}"
                
                # 转换为与 LLM 版本一致的格式
                requirements_list = []
                for i, doc in enumerate(topic_docs):
                    requirements_list.append({
                        'text': doc['req_content'] if isinstance(doc, dict) else str(doc),
                        'source': doc.get('source', f'需求_{topic_num_int}_{i}') if isinstance(doc, dict) else f'需求_{topic_num_int}_{i}',
                        'req_id': doc.get('req_id', '') if isinstance(doc, dict) else '',
                        'req_name': doc.get('req_name', '') if isinstance(doc, dict) else ''
                    })
                
                clustered_topics[topic_num_int] = {
                    'core_function': core_function,
                    'requirements': requirements_list
                }
        
        # 统计相似主题组（包含多个文档的主题）
        similar_topic_groups = {k: v for k, v in clustered_topics.items() if len(v['requirements']) > 1}
        
        save_data = {
            "metadata": {
                "total_requirements": int(len(abstracts)),
                "successful_analysis": int(len(abstracts)),  # BERTopic 处理所有需求
                "failed_analysis": int(len(failed_requirements)),
                "topic_groups": int(len(clustered_topics)),
                "similar_topic_groups": int(len(similar_topic_groups)),
                "standalone_requirements": int(len(standalone_topics)),
                "clustering_method": "BERTopic",
                "model_used": "BERTopic with sentence-transformers",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            },
            "similar_topic_groups": similar_topic_groups,
            "all_topic_groups": clustered_topics,
            "standalone_requirements": list(standalone_topics.values()),
            "failed_requirements": failed_requirements
        }
        
        try:
            # 使用类型转换函数确保所有数据都能被JSON序列化
            safe_save_data = convert_numpy_types(save_data)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(safe_save_data, f, indent=2, ensure_ascii=False)
            logging.info(f"主题聚类结果已保存到: {output_file}")
        except Exception as e:
            logging.error(f"保存聚类结果失败: {e}")

    return topic_model, topics_dict, abstracts


def cluster_similar_requirements_llm(re_dirs, api_key, model="gpt-4-turbo", max_retries=3, delay=2, save_results=True, similarity_threshold=0.7):
    """
    使用大模型识别软件产品线中的功能变体
    重点：识别实现相似功能但可能在实现方式、参数、技术选择等方面有差异的需求变体
    
    Args:
        api_key: OpenAI API 密钥
        model: 使用的 GPT 模型名称
        max_retries: API 调用最大重试次数
        delay: 重试间隔时间（秒）
        save_results: 是否保存聚类结果
        similarity_threshold: 功能相似度阈值（0.0-1.0，默认0.7适合变体识别）
        
    Returns:
        dict: 功能变体组字典
            {
                group_id: {
                    'core_function': '核心功能描述',
                    'requirements': [
                        {
                            'req_content': '需求原文',
                            'source': '来源产品',
                            'req_id': '需求ID',
                            'req_name': '需求名称'
                        }, ...
                    ]
                }
            }
    """
    logging.info("=== 使用大模型识别软件产品线功能变体 ===")
    
    # 初始化 OpenAI 客户端
    try:
        client = OpenAI(api_key=api_key, base_url=BASE_URL)
        logging.info("OpenAI 客户端初始化成功")
    except Exception as e:
        logging.error(f"OpenAI 客户端初始化失败: {e}")
        return {}

    # 加载需求摘要
    abstracts = load_requirement_dict(re_dirs)
    logging.info(f"加载了 {len(abstracts)} 个需求摘要，开始识别相同功能...")
    
    # 初始化功能组
    function_groups = {}
    next_group_id = 1
    processed_requirements = set()

    def _call_llm_for_function_matching(target_requirement, existing_groups):
        """调用LLM判断需求是否属于某个功能变体组"""
        
        # 构建现有功能组的描述
        groups_description = ""
        if existing_groups:
            groups_description = "已识别的功能变体组：\n"
            for group_id, group_info in existing_groups.items():
                groups_description += f"变体组 {group_id}: {group_info['core_function']}\n"
                groups_description += f"  已包含 {len(group_info['requirements'])} 个需求变体\n"
                # 显示第一个需求作为功能示例
                if group_info['requirements']:
                    first_req = group_info['requirements'][0]
                    example_text = str(first_req)
                    example = example_text[:100] + "..." if len(example_text) > 100 else example_text
                    groups_description += f"  变体示例: {example}\n"
                
                groups_description += "\n"
        else:
            groups_description = "当前还没有识别出任何功能变体组。\n"

        system_prompt = """你是一个专业的软件产品线分析师，专门识别软件产品相同功能的不同变体, 并把他们归为一组。

功能变体识别标准：
1. 核心功能目标严格相同
2. 核心交互对象严格相同(如用户、数据实体、系统组件等)
3. 功能描述在以下方面存在变异：
   - 某些部分技术选型（如不同的算法、框架、协议）
   - 参数配置（如阈值、时间限制、数据格式）
   - 性能要求（如实时性、准确性、吞吐量）
   - 其他非核心差异

输出格式（严格的JSON）：
{
    "matched_group_id": 匹配的功能组ID（整数，如果是新功能领域则为-1）,
    "core_function": "核心功能描述",
}

只输出JSON，不要包含其他内容。"""

        user_prompt = f"""请分析以下功能需求，判断是否属于某个现有功能组，或者其差异过大，值得新开功能组：

## 已有功能组
{groups_description}

## 待分析需求：
{target_requirement}

"""

        for attempt in range(max_retries):
            try:
                response = client.chat.completions.create(
                    model=model,
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt}
                    ],
                    temperature=0.2,  # 较低温度确保一致性
                    max_tokens=1000,
                    timeout=30
                )
                
                content = response.choices[0].message.content
                if not content:
                    return {"error": "API 返回空内容"}
                
                # 尝试解析JSON
                try:
                    result = json.loads(content.strip())
                    
                    # 验证返回格式 - 更新为新的字段名
                    required_keys = [ "matched_group_id", "core_function"]
                    if all(key in result for key in required_keys):
                        return result
                    else:
                        return {"error": f"响应缺少必要字段: {result}"}
                        
                except json.JSONDecodeError:
                    # 尝试提取JSON部分
                    json_match = re.search(r'\{.*\}', content.strip(), re.DOTALL)
                    if json_match:
                        try:
                            return json.loads(json_match.group(0))
                        except:
                            pass
                    return {"error": f"无法解析JSON响应: {content[:300]}..."}
                    
            except Exception as e:
                error_msg = str(e).lower()
                if attempt < max_retries - 1:
                    if "rate limit" in error_msg:
                        wait_time = delay * (2 ** attempt)
                        logging.warning(f"API 速率限制，等待 {wait_time} 秒后重试...")
                        time.sleep(wait_time)
                    elif "timeout" in error_msg or "api" in error_msg:
                        wait_time = delay * (attempt + 1)
                        logging.warning(f"API 错误，等待 {wait_time} 秒后重试...")
                        time.sleep(wait_time)
                    else:
                        logging.warning(f"未知错误，重试中: {e}")
                        time.sleep(delay)
                else:
                    return {"error": f"API 调用失败: {e}"}
        
        return {"error": "达到最大重试次数"}

    # 逐个处理需求，寻找功能变体
    failed_requirements = []
    
    for i, requirement in enumerate(abstracts, 1):
        if i in processed_requirements:
            continue
            
        logging.info(f"分析需求 {i}/{len(abstracts)}: {requirement['req_content'][:80]}...")
        
        # 调用LLM进行功能变体分析
        analysis = _call_llm_for_function_matching(str(requirement), function_groups)
        
        if "error" in analysis:
            logging.error(f"变体分析失败: {analysis['error']}")
            failed_requirements.append((i, requirement, analysis['error']))
            continue
            
        matched_group_id = analysis.get("matched_group_id", -1)
        core_function = analysis.get("core_function", "")
        
        if  matched_group_id in function_groups:
            # 找到功能变体，添加到现有组
            req_obj = {
                'req_content': requirement['req_content'],
                'source': requirement.get('source', ''),
                'req_id': requirement.get('req_id', ''),
                'req_name': requirement.get('req_name', '')
            }
            function_groups[matched_group_id]['requirements'].append(req_obj)
            
            # 更新核心功能描述（如果LLM提供了更好的抽象）
            if core_function and len(core_function) > len(function_groups[matched_group_id]['core_function']):
                function_groups[matched_group_id]['core_function'] = core_function
            
            logging.info(f"  -> 归入变体组 {matched_group_id}，当前包含 {len(function_groups[matched_group_id]['requirements'])} 个变体")
            
        else:
            # 新的功能领域，创建新组
            req_obj = {
                'req_content': requirement['req_content'],
                'source': requirement.get('source', ''),
                'req_id': requirement.get('req_id', ''),
                'req_name': requirement.get('req_name', '')
            }
            
            new_group = {
                'core_function': core_function or f"功能领域 {next_group_id}",
                'requirements': [req_obj],
            }
            function_groups[next_group_id] = new_group
            logging.info(f"  -> 创建新功能领域 {next_group_id}: {core_function}")
            next_group_id += 1
        
        processed_requirements.add(i)
        
        # 添加延迟避免API限制
        time.sleep(0.6)
    
    # 输出结果统计
    logging.info("=== 软件产品线功能变体识别结果 ===")
    logging.info(f"成功分析: {len(abstracts) - len(failed_requirements)} 个需求")
    logging.info(f"识别出功能领域: {len(function_groups)} 个")
    logging.info(f"分析失败: {len(failed_requirements)} 个")
    
    # 统计多变体的功能组
    variant_groups = {k: v for k, v in function_groups.items() if len(v['requirements']) > 1}
    single_requirement_groups = {k: v for k, v in function_groups.items() if len(v['requirements']) == 1}
    
    logging.info(f"包含多个变体的功能领域: {len(variant_groups)} 个")
    logging.info(f"单一需求的功能领域: {len(single_requirement_groups)} 个")
    
    for group_id, group_info in variant_groups.items():
        logging.info(f"\n功能领域 {group_id}: {group_info['core_function']}")
        logging.info(f"  包含 {len(group_info['requirements'])} 个变体")

        for j, req_info in enumerate(group_info['requirements'], 1):
            preview = req_info['req_content'][:100] + "..." if len(req_info['req_content']) > 100 else req_info['req_content']
            source = req_info.get('source', '未知来源')
            logging.info(f"    变体{j} ({source}): {preview}")

    # 保存结果（带时间戳文件名）
    if save_results:
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        folder = os.path.join(OUTPUT_DIR, "commonReq")
        os.makedirs(folder, exist_ok=True)
        filename = f"topic_clustering_results_llm_{timestamp}.json"
        output_file = os.path.join(folder, filename)
        
        # 保持与前面函数一致的保存格式
        save_data = {
            "metadata": {
                "total_requirements": len(abstracts),
                "successful_analysis": len(abstracts) - len(failed_requirements),
                "failed_analysis": len(failed_requirements),
                "topic_groups": len(function_groups),
                "similar_topic_groups": len(variant_groups),
                "standalone_requirements": len(single_requirement_groups),
                "similarity_threshold": similarity_threshold,
                "model_used": model,
                'clustering_method': "LLM-based analysis",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            },
            "similar_topic_groups": variant_groups,
            "standalone_requirements": single_requirement_groups,
            "all_topic_groups": function_groups,
            "failed_requirements": failed_requirements
        }
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)
            logging.info(f"产品线变体分析结果已保存到: {output_file}")
        except Exception as e:
            logging.error(f"保存结果失败: {e}")

    return function_groups
