# Cluster Similar Requirements 模块设计文档

## 概述

`cluster_similar_reqs.py` 模块提供了两种不同的需求聚类方法，用于识别软件产品线中相似功能的需求变体。该模块支持基于机器学习的 BERTopic 聚类和基于大语言模型（LLM）的智能分析，旨在帮助识别和归类具有相似核心功能但在实现细节上存在差异的需求。

## 功能特性

### 1. 双模式聚类支持
- **BERTopic 聚类**: 基于嵌入向量和无监督学习的自动主题发现
- **LLM 智能聚类**: 基于大语言模型的语义理解和功能变体识别

### 2. 软件产品线变体识别
- 识别实现相似功能但技术选型不同的需求
- 发现参数配置差异的功能变体
- 归类性能要求不同的同类需求

### 3. 多源数据处理
- 支持多个 JSONL 文件的批量处理
- 保留需求来源系统信息
- 维护需求 ID 和名称的完整性

### 4. 结果持久化
- JSON 格式的详细聚类结果
- 模型保存和重用机制
- 时间戳标记的版本管理

## 核心函数设计

### 1. 数据加载函数

#### `load_requirement_dict(re_dirs: str) -> List[Dict[str, str]]`

**功能**: 从指定目录加载多个系统的需求数据

**参数**:
- `re_dirs`: 包含 JSONL 文件的目录路径

**返回值**: 需求对象列表，每个对象包含：
- `req_id`: 需求 ID
- `req_content`: 需求内容
- `req_name`: 需求名称
- `source`: 来源系统名称

**设计特点**:
- 自动识别 JSONL 格式文件
- 使用文件名作为系统标识
- 错误容忍的 JSON 解析
- 空内容过滤机制

### 2. BERTopic 聚类函数

#### `cluster_similar_requirements(re_dirs, save_model=False, save_results=True)`

**功能**: 使用 BERTopic 进行无监督需求主题聚类

**技术栈**:
- **嵌入模型**: SentenceTransformer (可配置)
- **降维**: UMAP (n_neighbors=10, n_components=5)
- **聚类**: HDBSCAN (min_cluster_size=3)
- **向量化**: CountVectorizer (1-2元语法)
- **表示学习**: KeyBERTInspired

**工作流程**:
1. 加载预训练模型或创建新模型
2. 文本嵌入和特征提取
3. 降维和聚类处理
4. 主题词提取和表示
5. 结果格式化和保存

**参数配置**:
```python
umap_model = UMAP(
    n_neighbors=10,      # 邻居数量
    n_components=5,      # 降维后维度
    min_dist=0.1,        # 最小距离
    metric='cosine'      # 距离度量
)

hdbscan_model = HDBSCAN(
    min_cluster_size=3,                    # 最小集群大小
    metric='euclidean',                    # 距离度量
    cluster_selection_method='eom'         # 集群选择方法
)
```

### 3. LLM 智能聚类函数

#### `cluster_similar_requirements_llm(re_dirs, api_key, model="gpt-4-turbo", ...)`

**功能**: 使用大语言模型进行智能功能变体识别

**核心参数**:
- `api_key`: OpenAI API 密钥
- `model`: 使用的 GPT 模型
- `max_retries`: 最大重试次数 (默认 3)
- `delay`: 重试延迟时间 (默认 2秒)
- `similarity_threshold`: 相似度阈值 (默认 0.7)

**变体识别标准**:
1. **核心功能目标严格相同**
2. **核心交互对象严格相同**
3. **在以下方面存在变异**:
   - 技术选型差异
   - 参数配置不同
   - 性能要求差异
   - 其他非核心差异

**LLM Prompt 设计**:
```python
system_prompt = """你是一个专业的软件产品线分析师，专门识别软件产品相同功能的不同变体...

功能变体识别标准：
1. 核心功能目标严格相同
2. 核心交互对象严格相同
3. 功能描述在某些方面存在变异...

输出格式（严格的JSON）：
{
    "matched_group_id": 匹配的功能组ID,
    "core_function": "核心功能描述"
}
"""
```

## 数据结构设计

### 需求对象结构
```python
{
    "req_id": "需求唯一标识符",
    "req_content": "需求具体内容",
    "req_name": "需求名称或标题",
    "source": "来源系统名称"
}
```

### 聚类结果结构
```python
{
    "metadata": {
        "total_requirements": 总需求数量,
        "successful_analysis": 成功分析数量,
        "failed_analysis": 失败分析数量,
        "topic_groups": 主题组数量,
        "similar_topic_groups": 相似主题组数量,
        "standalone_requirements": 独立需求数量,
        "clustering_method": "聚类方法",
        "model_used": "使用的模型",
        "timestamp": "处理时间戳"
    },
    "similar_topic_groups": {
        group_id: {
            "core_function": "核心功能描述",
            "requirements": [需求对象列表]
        }
    },
    "all_topic_groups": 所有主题组,
    "standalone_requirements": 独立需求,
    "failed_requirements": 处理失败的需求
}
```

## 工具函数

### `convert_numpy_types(obj)`

**功能**: 递归转换 numpy 类型为 Python 原生类型

**支持转换**:
- `np.integer` → `int`
- `np.floating` → `float`
- `np.ndarray` → `list`
- 嵌套的字典、列表、元组

**用途**: 确保 JSON 序列化兼容性

## 错误处理机制

### API 调用错误处理
- **速率限制**: 指数退避重试策略
- **超时错误**: 线性增加重试延迟
- **JSON 解析失败**: 正则表达式提取修复
- **网络连接**: 自动重试机制

### 数据处理错误处理
- **文件读取异常**: 跳过损坏文件继续处理
- **JSON 格式错误**: 忽略格式错误的行
- **空内容过滤**: 自动过滤空白需求
- **编码问题**: UTF-8 强制编码

## 性能优化策略

### 1. 内存管理
- 流式处理大文件避免内存溢出
- 及时释放不需要的中间变量
- 使用生成器减少内存占用

### 2. 计算优化
- 模型复用机制减少重复训练
- 批量处理减少 API 调用次数
- 并行处理提升处理速度

### 3. API 调用优化
```python
# 速率控制
time.sleep(0.6)  # 避免 API 限制

# 重试策略
for attempt in range(max_retries):
    if "rate limit" in error_msg:
        wait_time = delay * (2 ** attempt)  # 指数退避
```

## 配置依赖

### 外部配置
- `BASE_URL`: OpenAI API 基础 URL
- `MODEL_DIR`: 模型存储目录
- `OUTPUT_DIR`: 输出结果目录
- `EMBEDDING_MODEL`: 嵌入模型路径
- `COMMONREQ_DATA`: 需求数据目录

### Python 依赖包
```python
# 机器学习相关
sentence_transformers    # 文本嵌入
bertopic                # 主题建模
umap-learn              # 降维
hdbscan                 # 聚类
scikit-learn           # 机器学习工具

# 数据处理
pandas                  # 数据分析
numpy                  # 数值计算

# NLP 相关
hanlp                  # 中文 NLP 工具包

# API 交互
openai                 # OpenAI API 客户端

# 标准库
json, os, re, time, collections
```

## 使用示例

### BERTopic 聚类
```python
# 设置参数
re_dirs = "data/commonreq"
save_model = True
save_results = True

# 执行聚类
topic_model, topics_dict, abstracts = cluster_similar_requirements(
    re_dirs=re_dirs,
    save_model=save_model,
    save_results=save_results
)

# 查看结果
print(f"发现 {len(topics_dict)} 个主题")
for topic_num, docs in topics_dict.items():
    print(f"主题 {topic_num}: {len(docs)} 个文档")
```

### LLM 智能聚类
```python
# 设置 API 参数
api_key = os.getenv("OPENAI_API_KEY")
model = "gpt-4-turbo"

# 执行变体识别
function_groups = cluster_similar_requirements_llm(
    re_dirs=re_dirs,
    api_key=api_key,
    model=model,
    similarity_threshold=0.7
)

# 查看变体组
for group_id, group_info in function_groups.items():
    if len(group_info['requirements']) > 1:
        print(f"功能变体组 {group_id}: {group_info['core_function']}")
        print(f"  包含 {len(group_info['requirements'])} 个变体")
```

## 输出文件格式

### BERTopic 结果文件
- **位置**: `{OUTPUT_DIR}/commonReq/topic_clustering_results.json`
- **格式**: 标准化的 JSON 格式
- **内容**: 主题分组、关键词、统计信息

### LLM 结果文件
- **位置**: `{OUTPUT_DIR}/commonReq/topic_clustering_results_llm_{timestamp}.json`
- **格式**: 带时间戳的 JSON 格式
- **内容**: 功能变体组、核心功能描述、详细分析

## 扩展建议

### 功能扩展
1. **多语言支持**: 扩展对不同语言需求的处理能力
2. **增量更新**: 支持新需求的增量聚类更新
3. **交互式调整**: 提供用户界面进行聚类结果调整
4. **质量评估**: 添加聚类质量评估指标

### 性能改进
1. **分布式处理**: 支持大规模数据的分布式聚类
2. **缓存机制**: 实现智能缓存减少重复计算
3. **并行优化**: 利用多线程/多进程提升处理速度
4. **内存优化**: 实现流式处理支持超大数据集

### 算法优化
1. **混合聚类**: 结合 BERTopic 和 LLM 的优势
2. **自适应参数**: 根据数据特征自动调整聚类参数
3. **层次聚类**: 实现多层次的需求组织结构
4. **动态聚类**: 支持聚类结果的动态调整和优化
