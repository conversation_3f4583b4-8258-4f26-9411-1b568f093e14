import json
import os
import re
from typing import List, Dict, Any, Tuple
from collections import defaultdict
from openai import OpenAI
import time
import logging
from commonReq.config import BASE_URL, OUTPUT_DIR

def cluster_processing_steps(
    requirement_group_data: Dict[str, Any], 
    api_key: str,
    model: str = "gpt-4-turbo",
    max_retries: int = 3,
    delay: int = 2,
    save_results: bool = True,
    similarity_threshold: float = 0.8
) -> Dict[str, Any]:
    """
    第一阶段：对需求组中的processing_steps进行动作聚类
    
    Args:
        requirement_group_data: 包含decomposed_requirements的需求组数据
        api_key: OpenAI API密钥
        model: 使用的GPT模型
        max_retries: 最大重试次数
        delay: 重试延迟
        save_results: 是否保存中间结果
        similarity_threshold: 动作相似度阈值
        
    Returns:
        Dict: 包含原数据和聚类结果的数据
    """
    logging.info("=== 第一阶段：动作聚类 ===")
    
    # 初始化OpenAI客户端
    try:
        client = OpenAI(api_key=api_key, base_url=BASE_URL)
        logging.info("OpenAI客户端初始化成功")
    except Exception as e:
        logging.error(f"OpenAI客户端初始化失败: {e}")
        return requirement_group_data
    
    # 提取所有processing_steps
    all_steps = []
    decomposed_reqs = requirement_group_data.get("decomposed_requirements", [])
    
    for req in decomposed_reqs:
        req_id = req.get("req_id", "")
        steps = req.get("processing_steps", [])
        for step in steps:
            all_steps.append({
                "step": step,
                "req_id": req_id,
                "original_index": len(all_steps),
                "req_step_count": len(steps)  # 添加该需求的总步骤数
            })
    
    # 按步骤数量排序：优先处理来自步骤少的需求的步骤
    all_steps.sort(key=lambda x: (x["req_step_count"], x["original_index"]))
    logging.info(f"共收集到 {len(all_steps)} 个处理步骤，已按需求步骤数量排序（优先处理步骤少的需求）")
    
    # 逐条聚类
    action_clusters = {}
    next_cluster_id = 1
    failed_steps = []
    
    def _call_llm_for_action_clustering(target_step: str, existing_clusters: Dict) -> Dict:
        """调用LLM判断动作步骤是否可以合并到现有类别"""
        
        # 构建现有动作类别的描述
        clusters_description = ""
        if existing_clusters:
            clusters_description = "当前已识别的动作类别：\n"
            for cluster_id, cluster_info in existing_clusters.items():
                clusters_description += f"类别 {cluster_id}: {cluster_info.get('temp_description', '待定义')}\n"
                clusters_description += f"  包含动作: {len(cluster_info['steps'])} 个\n"
                # 显示前3个动作作为示例
                examples = cluster_info['steps'][:3]
                for i, example in enumerate(examples, 1):
                    clusters_description += f"    示例{i}: {example['step']}\n"
                clusters_description += "\n"
        else:
            clusters_description = "当前还没有识别出任何动作类别。\n"

        system_prompt = """你是一个专业的软件需求分析师，专门识别和聚类软件处理步骤中的核心动作。

你的任务是判断一个新的处理步骤是否与现有动作类别描述相同的核心动作，还是代表一个全新的动作类型。

核心动作等价的判断标准：
1. 核心谓词相同或语义极其相近（如"建立"与"创建"、"监控"与"检测"）
2. 动作的对象类型相同或相似（如"通信连接"与"通信链路"）
3. 动作的目的和效果基本一致
4. 只是描述方式、具体实现细节不同
5. 上位词关系：如果某个动作是另一个动作的上位词或下位词，它们应该归为同一类
   例如："数据采集"（上位词）与"飞行数据采集"（下位词）应该归为一类
   例如："控制执行"（上位词）与"执行机构控制"（下位词）应该归为一类

匹配的例子：
- "系统建立通信连接" 和 "系统创建通信链路" → 相同核心动作（建立通信）
- "系统监控数据传输" 和 "系统检测传输状态" → 相同核心动作（监控传输）
- "系统加密数据包" 和 "系统对数据进行加密处理" → 相同核心动作（数据加密）
- "系统采集数据" 和 "系统实时采集飞行数据" → 相同核心动作（数据采集，上下位词关系）
- "系统控制执行" 和 "系统驱动执行机构" → 相同核心动作（控制执行，上下位词关系）

不能合并的例子：
- "建立连接" vs "断开连接" → 不同动作（相反操作）
- "数据加密" vs "数据解密" → 不同动作（相反操作）
- "发送数据" vs "接收数据" → 不同动作（不同方向）
- "启动系统" vs "监控系统" → 不同动作（不同目的）

输出格式（严格JSON）：
{
    "is_same_action": true/false,
    "matched_cluster_id": 匹配的类别ID（整数，如果是新动作则为-1）,
    "core_action_description": "核心动作的简洁描述（如：建立通信、监控数据、加密处理等）"
}

只输出JSON，不要包含其他内容。"""

        user_prompt = f"""请分析以下处理步骤，判断是否与某个现有动作类别描述相同的核心动作：

{clusters_description}

待分析步骤：
{target_step}

请判断这个步骤是否与某个现有类别描述相同的核心动作，或者是一个新的独特动作类型。"""

        for attempt in range(max_retries):
            try:
                response = client.chat.completions.create(
                    model=model,
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt}
                    ],
                    temperature=0.2,
                    max_tokens=800,
                    timeout=30
                )
                
                content = response.choices[0].message.content
                if not content:
                    return {"error": "API返回空内容"}
                
                try:
                    result = json.loads(content.strip())
                    required_keys = ["is_same_action", "matched_cluster_id", "core_action_description"]
                    if all(key in result for key in required_keys):
                        return result
                    else:
                        return {"error": f"响应缺少必要字段: {result}"}
                except json.JSONDecodeError:
                    json_match = re.search(r'\{.*\}', content.strip(), re.DOTALL)
                    if json_match:
                        try:
                            return json.loads(json_match.group(0))
                        except:
                            pass
                    return {"error": f"无法解析JSON响应: {content[:200]}..."}
                    
            except Exception as e:
                if attempt < max_retries - 1:
                    wait_time = delay * (attempt + 1)
                    logging.warning(f"API调用失败，等待{wait_time}秒后重试: {e}")
                    time.sleep(wait_time)
                else:
                    return {"error": f"API调用失败: {e}"}
        
        return {"error": "达到最大重试次数"}
    
    # 逐步聚类处理
    for i, step_info in enumerate(all_steps, 1):
        step_text = step_info["step"]
        req_id = step_info["req_id"]
        req_step_count = step_info["req_step_count"]
        
        logging.info(f"分析步骤 {i}/{len(all_steps)} ({req_id}, 需求含{req_step_count}步): {step_text[:50]}...")
        
        # 调用LLM进行聚类分析
        analysis = _call_llm_for_action_clustering(step_text, action_clusters)
        
        if "error" in analysis:
            logging.error(f"聚类分析失败: {analysis['error']}")
            failed_steps.append((i, step_info, analysis['error']))
            continue
        
        is_same_action = analysis.get("is_same_action", False)
        matched_cluster_id = analysis.get("matched_cluster_id", -1)
        core_action_description = analysis.get("core_action_description", "")
        
        logging.info(f"  相同动作: {is_same_action}")
        
        # 简化置信度检查，使用默认的高置信度
        confidence = 1.0 if is_same_action else 0.9
        if confidence < similarity_threshold:
            logging.info(f"  -> 置信度过低({confidence:.2f} < {similarity_threshold})，创建新类别")
            is_same_action = False
        
        if is_same_action and matched_cluster_id in action_clusters:
            # 添加到现有类别
            action_clusters[matched_cluster_id]['steps'].append(step_info)
            # 更新临时描述（如果新描述更好）
            current_desc = action_clusters[matched_cluster_id].get('temp_description', '')
            if len(core_action_description) > len(current_desc):
                action_clusters[matched_cluster_id]['temp_description'] = core_action_description
            
            logging.info(f"  -> 归入类别 {matched_cluster_id}，当前包含 {len(action_clusters[matched_cluster_id]['steps'])} 个步骤")
        else:
            # 创建新类别
            action_clusters[next_cluster_id] = {
                'steps': [step_info],
                'temp_description': core_action_description or f"动作类别 {next_cluster_id}"
            }
            logging.info(f"  -> 创建新类别 {next_cluster_id}: {core_action_description}")
            next_cluster_id += 1
        
        # API限制延迟
        time.sleep(0.6)
    
    # 构建第一阶段结果
    result_data = requirement_group_data.copy()
    result_data["action_clusters"] = action_clusters
    result_data["clustering_metadata"] = {
        "total_steps": len(all_steps),
        "action_clusters": len(action_clusters),
        "failed_steps": len(failed_steps),
        "failed_step_details": failed_steps,
        "similarity_threshold": similarity_threshold,
        "model_used": model,
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
    }
    
    # 输出聚类统计结果
    logging.info("=== 第一阶段聚类结果统计 ===")
    logging.info(f"原始步骤总数: {len(all_steps)}")
    logging.info(f"识别动作类别: {len(action_clusters)}")
    logging.info(f"聚类失败: {len(failed_steps)}")
    
    logging.info("\n动作类别概览:")
    for cluster_id, cluster_info in action_clusters.items():
        temp_desc = cluster_info.get('temp_description', f'类别{cluster_id}')
        step_count = len(cluster_info['steps'])
        logging.info(f"  类别 {cluster_id} ({temp_desc}): {step_count} 个步骤")
    
    # 保存第一阶段结果
    if save_results:
        core_function = requirement_group_data.get("core_function", "unknown")
        safe_filename = re.sub(r'[^\w\s-]', '', core_function)[:50]
        output_file = os.path.join(OUTPUT_DIR, "commonReq", f"action_clusters_{safe_filename}.json")
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, indent=2, ensure_ascii=False)
            logging.info(f"第一阶段聚类结果已保存到: {output_file}")
        except Exception as e:
            logging.error(f"保存第一阶段结果失败: {e}")
    
    return result_data


def standardize_action_clusters(
    clustered_data: Dict[str, Any],
    api_key: str,
    model: str = "gpt-4-turbo",
    max_retries: int = 3,
    delay: int = 2,
    save_results: bool = True
) -> Dict[str, Any]:
    """
    第二阶段：对动作类别进行标准化
    
    Args:
        clustered_data: 第一阶段的聚类结果数据
        api_key: OpenAI API密钥
        model: 使用的GPT模型
        max_retries: 最大重试次数
        delay: 重试延迟
        save_results: 是否保存结果
        
    Returns:
        Dict: 包含标准化动作映射的完整结果
    """
    logging.info("=== 第二阶段：动作类别标准化 ===")
    
    # 初始化OpenAI客户端
    try:
        client = OpenAI(api_key=api_key, base_url=BASE_URL)
        logging.info("OpenAI客户端初始化成功")
    except Exception as e:
        logging.error(f"OpenAI客户端初始化失败: {e}")
        return clustered_data
    
    # 获取聚类结果
    action_clusters = clustered_data.get("action_clusters", {})
    if not action_clusters:
        logging.error("输入数据中没有找到动作聚类结果")
        return clustered_data
    
    def _call_llm_for_standardization(cluster_steps: List[Dict]) -> Dict:
        """调用LLM对动作类别进行标准化总结"""
        
        steps_text = "\n".join([f"- {step['step']}" for step in cluster_steps])
        
        system_prompt = """你是一个专业的软件需求分析师，专门对相似的处理步骤进行标准化总结。

你的任务是为一组相似的处理步骤创建一个标准化的动作描述，该描述应该与具体业务场景密切关联。

标准化要求：
1. 保留业务领域的专业术语和关键对象（如：飞行姿态、通信链路、用户认证、数据库事务等）
2. 提取核心动作，但结合具体业务对象（如：采集、计算、执行、监控、建立等）
3. 保持足够的业务特异性，避免过于通用的描述
4. 简洁明了，但能准确反映业务含义
5. 保持一致的格式风格：业务对象 + 动作

业务相关的标准化例子：
- 多个关于"实时采集飞行姿态、速度数据"的步骤 → "飞行数据采集"
- 多个关于"计算飞行调整指令"的步骤 → "飞行调整指令计算"  
- 多个关于"驱动飞行执行机构"的步骤 → "飞行执行机构驱动"
- 多个关于"建立无人机通信连接"的步骤 → "无人机通信建立"

错误的过于通用的例子（避免）：
- "数据处理" 
- "指令执行" 

输出格式（严格JSON）：
{
    "standardized_action": "标准化的动作名称（业务特定且简洁）"
}

只输出JSON，不要包含其他内容。"""

        user_prompt = f"""请为以下相似的处理步骤组创建标准化的动作描述：

处理步骤组：
{steps_text}

请提供一个统一、标准化的动作名称和描述。"""

        for attempt in range(max_retries):
            try:
                response = client.chat.completions.create(
                    model=model,
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt}
                    ],
                    temperature=0.3,
                    max_tokens=600,
                    timeout=30
                )
                
                content = response.choices[0].message.content
                if not content:
                    return {"error": "API返回空内容"}
                
                try:
                    result = json.loads(content.strip())
                    required_keys = ["standardized_action"]
                    if all(key in result for key in required_keys):
                        return result
                    else:
                        return {"error": f"响应缺少必要字段: {result}"}
                except json.JSONDecodeError:
                    json_match = re.search(r'\{.*\}', content.strip(), re.DOTALL)
                    if json_match:
                        try:
                            return json.loads(json_match.group(0))
                        except:
                            pass
                    return {"error": f"无法解析JSON响应: {content[:200]}..."}
                    
            except Exception as e:
                if attempt < max_retries - 1:
                    wait_time = delay * (attempt + 1)
                    logging.warning(f"标准化API调用失败，等待{wait_time}秒后重试: {e}")
                    time.sleep(wait_time)
                else:
                    return {"error": f"标准化API调用失败: {e}"}
        
        return {"error": "标准化达到最大重试次数"}
    
    # 对每个类别进行标准化
    standardized_actions = {}
    failed_standardizations = []
    
    for cluster_id, cluster_info in action_clusters.items():
        steps = cluster_info['steps']
        temp_desc = cluster_info.get('temp_description', f'类别{cluster_id}')
        
        logging.info(f"标准化类别 {cluster_id} ({temp_desc}): {len(steps)} 个步骤")
        
        # 调用LLM进行标准化
        standardization = _call_llm_for_standardization(steps)
        
        if "error" in standardization:
            logging.error(f"类别 {cluster_id} 标准化失败: {standardization['error']}")
            failed_standardizations.append((cluster_id, standardization['error']))
            # 使用临时描述作为备选
            standardized_action = temp_desc
        else:
            standardized_action = standardization.get("standardized_action", temp_desc)
        
        # 构建最终的映射结果（包含完整溯源信息）
        standardized_actions[standardized_action] = {
            "steps_with_source": [
                {
                    "step": step['step'],
                    "req_id": step['req_id'],
                    "original_index": step['original_index']
                } for step in steps
            ],
            "req_sources": list(set([step['req_id'] for step in steps])),
            "step_count": len(steps),
            "cluster_id": cluster_id
        }
        
        logging.info(f"  -> 标准化为: {standardized_action}")
        time.sleep(0.6)
    
    # 构建第二阶段结果
    result_data = clustered_data.copy()
    result_data["standardized_actions"] = standardized_actions
    result_data["standardization_metadata"] = {
        "action_clusters": len(action_clusters),
        "successful_standardizations": len(standardized_actions) - len(failed_standardizations),
        "failed_standardizations": len(failed_standardizations),
        "failed_standardization_details": failed_standardizations,
        "model_used": model,
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
    }
    
    # 输出第二阶段统计结果
    logging.info("=== 第二阶段标准化结果统计 ===")
    logging.info(f"动作类别总数: {len(action_clusters)}")
    logging.info(f"标准化成功: {len(standardized_actions) - len(failed_standardizations)}")
    logging.info(f"标准化失败: {len(failed_standardizations)}")
    
    logging.info("\n标准化动作列表:")
    for std_action, details in standardized_actions.items():
        logging.info(f"  {std_action}: {details['step_count']} 个原始步骤")
    
    # 保存第二阶段结果
    if save_results:
        core_function = clustered_data.get("core_function", "unknown")
        safe_filename = re.sub(r'[^\w\s-]', '', core_function)[:50]
        output_file = os.path.join(OUTPUT_DIR, "commonReq", f"standardized_actions_{safe_filename}.json")
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, indent=2, ensure_ascii=False)
            logging.info(f"第二阶段标准化结果已保存到: {output_file}")
        except Exception as e:
            logging.error(f"保存第二阶段结果失败: {e}")
    
    return result_data


def cluster_and_standardize_actions(
    decomposed_req: Dict[str, Any], 
    api_key: str,
    model: str = "gpt-4-turbo",
    max_retries: int = 3,
    delay: int = 2,
    save_results: bool = True,
    similarity_threshold: float = 0.8
) -> Dict[str, Any]:
    """
    完整的两阶段动作聚类和标准化流程（便捷函数）
    
    Args:
        requirement_group_data: 包含decomposed_requirements的需求组数据
        api_key: OpenAI API密钥
        model: 使用的GPT模型
        max_retries: 最大重试次数
        delay: 重试延迟
        save_results: 是否保存结果
        similarity_threshold: 动作相似度阈值
        
    Returns:
        Dict: 包含标准化动作映射的完整结果
    """
    logging.info("=== 开始完整的动作聚类与标准化流程 ===")
    # 先复制一份，避免修改原始数据
    decomposed_req = json.loads(json.dumps(decomposed_req))
    for group_id, req_group in decomposed_req['decomposed_function_groups'].items():
        logging.info(f"处理功能组 {group_id}，包含 {len(req_group['decomposed_requirements'])} 个需求")
        # 第一阶段：聚类
        clustered_result = cluster_processing_steps(
            req_group, api_key, model, max_retries, delay, save_results, similarity_threshold
        )
        # 第二阶段：标准化
        final_result = standardize_action_clusters(
            clustered_result, api_key, model, max_retries, delay, save_results
        )
        # 将标准化结果保存回原始数据结构
        decomposed_req['decomposed_function_groups'][group_id] = final_result
    logging.info("=== 完整流程完成 ===")
    if save_results:
        # 保存结果到JSON文件
        os.makedirs(os.path.join(OUTPUT_DIR, "commonReq"), exist_ok=True)
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(OUTPUT_DIR, "commonReq", f"cluster_and_standardized_reqs_{timestamp}.json")
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(decomposed_req, f, ensure_ascii=False, indent=2)
            logging.info(f"动作聚类与标准化结果已保存到: {output_file}")
        except Exception as e:
            logging.error(f"保存动作聚类与标准化结果失败: {e}")
    else:
        logging.info("...跳过保存动作聚类与标准化结果")
    return final_result


def demo_stage_one_clustering():
    """演示第一阶段：动作聚类"""
    # 示例数据（从用户输入复制）
    sample_data = {
        "core_function": "无人机飞行姿态、速度、位置数据的实时采集，飞行调整指令的计算与执行，多种飞行模式支持，以及稳定飞行和抗干扰能力",
        "original_requirements": [
            {
                "req_content": "无人机系统的飞行控制模块必须能够实时采集飞行姿态、速度和位置等关键数据，通过精准的算法计算出飞行调整指令，并可靠地驱动执行机构，使无人机在各种复杂环境中保持稳定飞行，具备应对外界干扰的能力。模块需支持多种飞行模式，如手动控制、自动驾驶以及任务规划导航模式，并确保在极端条件下仍具备高可靠性和容错性。",
                "req_id": "需求_1"
            },
            {
                "req_content": "无人机系统的飞行控制模块需要在飞行过程中持续采集并监测姿态、速度以及高度等关键数据，通过高精度算法生成最优调整指令，并驱动执行机构以在各种复杂环境下保持平稳飞行。该模块需具备快速响应外界干扰的能力，支持手动控制、半自动以及预设任务自主飞行等多种模式，并在极端气象或突发情况下提供高可靠性和故障安全机制。",
                "req_id": "需求_11"
            }
        ],
        "decomposed_requirements": [
            {
                "req_id": "需求_1",
                "req_name": "功能组1-需求1",
                "processing_steps": [
                    "系统实时采集飞行姿态、速度和位置数据",
                    "系统通过精准算法计算飞行调整指令",
                    "系统可靠驱动执行机构",
                    "系统应对外界干扰以保持稳定飞行",
                    "系统支持手动控制模式"
                ]
            },
            {
                "req_id": "需求_11", 
                "req_name": "功能组1-需求2",
                "processing_steps": [
                    "系统持续采集姿态、速度、高度数据",
                    "系统监测关键数据",
                    "系统通过高精度算法生成最优调整指令",
                    "系统驱动执行机构保持平稳飞行",
                    "系统快速响应外界干扰"
                ]
            }
        ]
    }
    
    # 注意：这里需要提供真实的API密钥
    api_key = "your_openai_api_key_here"
    
    print("开始演示第一阶段：动作聚类...")
    print("注意：请替换真实的OpenAI API密钥才能运行")
    
    # 如果有真实的API密钥，可以取消下面的注释
    # clustered_result = cluster_processing_steps(sample_data, api_key)
    # print("\n=== 第一阶段聚类结果 ===")
    # print(json.dumps(clustered_result["action_clusters"], indent=2, ensure_ascii=False))
    # print("\n=== 聚类元数据 ===")
    # print(json.dumps(clustered_result["clustering_metadata"], indent=2, ensure_ascii=False))
    
    return sample_data


def demo_stage_two_standardization():
    """演示第二阶段：动作标准化（使用模拟的聚类数据）"""
    # 模拟第一阶段的聚类结果
    clustered_data = {
        "core_function": "无人机飞行控制",
        "action_clusters": {
            1: {
                "steps": [
                    {"step": "系统实时采集飞行姿态、速度和位置数据", "req_id": "需求_1", "original_index": 0},
                    {"step": "系统持续采集姿态、速度、高度数据", "req_id": "需求_11", "original_index": 5}
                ],
                "temp_description": "数据采集"
            },
            2: {
                "steps": [
                    {"step": "系统通过精准算法计算飞行调整指令", "req_id": "需求_1", "original_index": 1},
                    {"step": "系统通过高精度算法生成最优调整指令", "req_id": "需求_11", "original_index": 7}
                ],
                "temp_description": "指令计算"
            },
            3: {
                "steps": [
                    {"step": "系统可靠驱动执行机构", "req_id": "需求_1", "original_index": 2},
                    {"step": "系统驱动执行机构保持平稳飞行", "req_id": "需求_11", "original_index": 8}
                ],
                "temp_description": "机构驱动"
            }
        }
    }
    
    api_key = "your_openai_api_key_here"
    
    print("开始演示第二阶段：动作标准化...")
    print("注意：请替换真实的OpenAI API密钥才能运行")
    
    # 如果有真实的API密钥，可以取消下面的注释
    # standardized_result = standardize_action_clusters(clustered_data, api_key)
    # print("\n=== 第二阶段标准化结果 ===")
    # print(json.dumps(standardized_result["standardized_actions"], indent=2, ensure_ascii=False))
    # print("\n=== 标准化元数据 ===")
    # print(json.dumps(standardized_result["standardization_metadata"], indent=2, ensure_ascii=False))
    
    return clustered_data


def demo_improved_clustering():
    """演示改进后的聚类效果：步骤排序和上位词识别"""
    print("=== 改进演示：优化的动作聚类 ===")
    
    # 示例数据，模拟不同复杂度的需求
    sample_data = {
        "core_function": "演示改进的聚类算法",
        "decomposed_requirements": [
            {
                "req_id": "简单需求_1",
                "processing_steps": [
                    "系统采集数据",
                    "系统控制执行"
                ]
            },
            {
                "req_id": "复杂需求_2", 
                "processing_steps": [
                    "系统实时采集飞行姿态数据",
                    "系统通过算法计算调整指令",
                    "系统驱动执行机构控制飞行",
                    "系统监控飞行状态",
                    "系统响应外界干扰"
                ]
            },
            {
                "req_id": "中等需求_3",
                "processing_steps": [
                    "系统采集传感器数据",
                    "系统执行控制指令",
                    "系统检测系统状态"
                ]
            }
        ]
    }
    
    # 显示排序前的步骤
    print("\n原始步骤顺序（未排序）：")
    original_steps = []
    for req in sample_data["decomposed_requirements"]:
        for step in req["processing_steps"]:
            original_steps.append(f"{req['req_id']} ({len(req['processing_steps'])}步): {step}")
    
    for i, step in enumerate(original_steps, 1):
        print(f"  {i}. {step}")
    
    # 模拟排序后的效果
    all_steps = []
    for req in sample_data["decomposed_requirements"]:
        for step in req["processing_steps"]:
            all_steps.append({
                "step": step,
                "req_id": req["req_id"],
                "req_step_count": len(req["processing_steps"])
            })
    
    # 按步骤数量排序
    all_steps.sort(key=lambda x: (x["req_step_count"], x["step"]))
    
    print("\n改进后的处理顺序（按需求复杂度排序）：")
    for i, step_info in enumerate(all_steps, 1):
        print(f"  {i}. {step_info['req_id']} ({step_info['req_step_count']}步): {step_info['step']}")
    
    print("\n改进说明：")
    print("1. 📊 优先处理步骤少的需求，建立基础动作类别")
    print("2. 🔍 增强上位词/下位词识别能力")
    print("   - '系统采集数据' ← → '系统实时采集飞行姿态数据'")
    print("   - '系统控制执行' ← → '系统驱动执行机构控制飞行'")
    print("3. ⚡ 简化LLM输出，提高处理速度")
    print("4. 🎯 更准确的动作分类结果")


def demo_complete_workflow():
    """演示完整的两阶段工作流程"""
    sample_data = demo_stage_one_clustering()
    
    print("\n" + "="*50)
    print("分阶段处理演示：")
    print("1. 第一阶段会生成 action_clusters 和 clustering_metadata")
    print("2. 第二阶段会基于第一阶段结果生成 standardized_actions 和 standardization_metadata") 
    print("3. 你可以在两个阶段之间检查和调整中间结果")
    print("="*50)
    
    demo_stage_two_standardization()
    
    # 展示改进效果
    demo_improved_clustering()


if __name__ == "__main__":
    demo_complete_workflow()