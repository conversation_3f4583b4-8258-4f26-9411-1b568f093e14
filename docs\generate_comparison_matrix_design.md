# Generate Comparison Matrix 模块设计文档

## 概述

`generate_comparison_matrix.py` 模块实现了需求可变性分析功能，通过提取和分析语义元组中的参数差异，识别软件产品线中的配置变化点。该模块将语义角色标注的结果转换为二维参数矩阵，并使用大语言模型进行智能分析，生成结构化的可变性配置项。

## 功能特性

### 1. 语义元组提取与转换
- 从标准化动作中提取所有语义元组
- 将语义元组展开为二维参数矩阵
- 支持多种语义角色类型的处理

### 2. 智能参数对比分析
- 基于 LLM 的参数差异识别
- 自动归纳配置维度和候选项
- 避免重复和相似的配置项生成

### 3. 可变性配置项生成
- 结构化的配置项输出
- 互斥且不重复的候选项设计
- 业务语义的准确保持

## 核心函数设计

### 1. 语义元组提取

#### `extract_semantic_tuples(standardized_data) -> Dict[str, List[List[Any]]]`

**功能**: 从 standardized_actions 中提取所有 semantic_tuples

**输入结构**:
```python
standardized_data = {
    "standardized_actions": {
        "飞行数据采集": {
            "steps_with_source": [
                {
                    "step": "系统实时采集飞行姿态数据",
                    "semantic_tuples": [
                        ["采集", "ARG0", "系统", "ARG1", "飞行姿态数据", "ARGM-MNR", "实时"]
                    ]
                }
            ]
        }
    }
}
```

**输出结构**:
```python
{
    "飞行数据采集": [
        ["采集", "ARG0", "系统", "ARG1", "飞行姿态数据", "ARGM-MNR", "实时"],
        ["采集", "ARG0", "系统", "ARG1", "速度数据", "ARGM-MNR", "持续"]
    ]
}
```

### 2. 矩阵展开转换

#### `expand_tuples_to_matrix(semantic_tuples) -> pd.DataFrame`

**功能**: 将语义元组展开为二维参数矩阵

**转换逻辑**:
1. **收集参数类型**: 统计所有可能的参数类型（ARG0, ARG1, ARGM-MNR 等）
2. **创建列结构**: 谓语 + 所有参数类型作为列名
3. **填充矩阵**: 每个元组对应一行，按参数类型填充内容

**示例转换**:
```python
# 输入语义元组
[
    ["采集", "ARG0", "系统", "ARG1", "飞行姿态数据", "ARGM-MNR", "实时"],
    ["采集", "ARG0", "系统", "ARG1", "速度数据", "ARGM-MNR", "持续"]
]

# 转换为DataFrame
   predicate  ARG0  ARG1      ARGM-MNR
0      采集    系统  飞行姿态数据     实时
1      采集    系统   速度数据      持续
```

**技术特点**:
- 自动识别所有参数类型
- 处理参数缺失的情况
- 支持重复参数类型的合并

### 3. LLM 参数对比分析

#### `compare_parameters_with_llm(df, action_name, api_key, ...) -> Dict[str, List[Dict[str, Any]]]`

**功能**: 使用 LLM 对比参数中的配置项差异

**核心设计原则**:
1. **严格基于数据**: 仅基于提供的参数值进行分析
2. **避免重复**: 从参数值的核心差异点归纳配置维度
3. **互斥候选**: 候选项应该是互斥的、不重复的
4. **业务特异**: 保持业务语义的准确性

**LLM Prompt 设计**:
```python
system_prompt = """你是一个需求分析专家，负责从给定的参数值中归纳出配置项。

重要原则：
1. 严格基于提供的参数值进行分析，不可推断或臆断不存在的内容
2. 从参数值的核心差异点归纳配置维度，避免产生重复或相似的配置项
3. 配置项名称必须简洁明确，直接反映参数值的本质差异
4. 候选项应该是互斥的、不重复的，代表不同的配置选择

语义角色说明：
- ARG1: 受事者（动作对象）
- ARG2: 起点、来源、材料等
- ARGM-MNR: 方式
- ARGM-TMP: 时间
- ...

返回JSON格式：
[
    {
        "配置项名称": "简洁明确的配置维度名称",
        "候选项": ["选项1", "选项2", "选项3"]
    }
]"""
```

**分析流程**:
1. 过滤需要对比的参数类型（忽略谓语和 ARG0）
2. 提取参数的所有唯一值
3. 调用 LLM 识别配置维度和候选项
4. 验证和格式化结果

### 4. 主分析函数

#### `analyze_requirement_variability(standardized_data, api_key, ...) -> Dict[str, Any]`

**功能**: 需求可变性分析的主函数

**完整处理流程**:
```python
def analyze_requirement_variability():
    # 步骤1: 提取语义元组
    semantic_tuples_by_action = extract_semantic_tuples(standardized_data)
    
    # 步骤2-4: 对每个动作分析
    for action_name, semantic_tuples in semantic_tuples_by_action.items():
        # 步骤2: 展开为二维矩阵
        df_matrix = expand_tuples_to_matrix(semantic_tuples)
        
        # 步骤3: LLM对比分析
        config_items = compare_parameters_with_llm(df_matrix, action_name, api_key)
        
        # 步骤4: 保存结果
        result_data["standardized_actions"][action_name]["variability_config"] = config_items
    
    return result_data
```

### 5. 统一接口函数

#### `run_analyze_requirement_variability(clustered_data, api_key, ...) -> Dict[str, Any]`

**功能**: 支持分组和单组数据的统一处理接口

**数据结构适配**:
```python
# 检查数据结构
if 'decomposed_function_groups' in clustered_data:
    # 处理分组数据结构
    for group_id, req_group in clustered_data['decomposed_function_groups'].items():
        res = analyze_requirement_variability(req_group, api_key, ...)
        clustered_data['decomposed_function_groups'][group_id] = res
else:
    # 处理单组数据结构  
    clustered_data = analyze_requirement_variability(clustered_data, api_key, ...)
```

## 数据结构设计

### 输入数据结构
```python
{
    "standardized_actions": {
        "动作名称": {
            "steps_with_source": [
                {
                    "step": "处理步骤文本",
                    "semantic_tuples": [
                        ["谓词", "ARG0", "施事者", "ARG1", "受事者", "ARGM-MNR", "方式"]
                    ]
                }
            ]
        }
    }
}
```

### 二维矩阵结构
```python
# DataFrame格式
   predicate  ARG0  ARG1        ARG2      ARGM-MNR  ARGM-TMP
0      采集    系统  飞行姿态数据    传感器       实时      无
1      采集    系统   速度数据      GPS       持续      无
2      计算    系统   调整指令      算法       精准      无
```

### 输出数据结构
```python
{
    "standardized_actions": {
        "飞行数据采集": {
            "steps_with_source": [...],
            "variability_config": {
                "ARG1": [  # 受事者参数类型
                    {
                        "配置项名称": "数据采集对象",
                        "候选项": ["飞行姿态数据", "速度数据", "位置数据"]
                    }
                ],
                "ARGM-MNR": [  # 方式参数类型
                    {
                        "配置项名称": "采集方式", 
                        "候选项": ["实时", "持续", "定时"]
                    }
                ]
            }
        }
    }
}
```

## 核心算法特性

### 1. 参数类型过滤
```python
# 需要对比的参数类型（忽略谓语和ARG0）
param_types_to_compare = [col for col in df.columns if col not in ["predicate", "ARG0"]]
```

**过滤逻辑**:
- **忽略谓语**: 已经在动作级别处理
- **忽略 ARG0**: 通常是"系统"，无变化价值
- **关注其他角色**: ARG1, ARG2, ARGM-* 等包含配置差异

### 2. 参数值预处理
```python
# 获取参数唯一值并去重
param_values = df[param_type].dropna()
param_values = [str(val).strip() for val in param_values if str(val).strip()]

# 去重但保持顺序
unique_values = []
seen = set()
for val in param_values:
    if val not in seen:
        unique_values.append(val)
        seen.add(val)
```

### 3. 配置项验证
```python
def validate_config_item(item):
    required_keys = ["配置项名称", "候选项"]
    if not all(key in item for key in required_keys):
        return False
    if not isinstance(item["候选项"], list) or len(item["候选项"]) == 0:
        return False
    if not item["配置项名称"].strip():
        return False
    return True
```

## 错误处理机制

### API 调用错误处理
```python
def _call_llm_for_config_analysis(client, system_prompt, user_prompt, model, max_retries):
    for attempt in range(max_retries):
        try:
            response = client.chat.completions.create(...)
            content = response.choices[0].message.content
            
            # JSON解析
            result = json.loads(content.strip())
            return validate_and_filter_configs(result)
            
        except json.JSONDecodeError:
            # 尝试正则表达式提取JSON
            json_match = re.search(r'\[.*\]', content.strip(), re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group(0))
                except: pass
                    
        except Exception as e:
            if attempt < max_retries - 1:
                wait_time = 2 * (attempt + 1)
                time.sleep(wait_time)
    
    return []
```

### 数据验证机制
- **JSON 格式验证**: 确保 API 返回正确格式
- **字段完整性检查**: 验证必需字段存在
- **数据类型验证**: 确保字段类型正确
- **业务逻辑验证**: 检查候选项的合理性

## 性能优化策略

### 1. 内存管理
- 使用 Pandas DataFrame 高效处理矩阵数据
- 及时释放不需要的中间数据
- 流式处理避免内存溢出

### 2. API 调用优化
```python
# 速率控制
time.sleep(1)  # 动作间延迟
time.sleep(0.5)  # 组间延迟

# 批处理优化
# 一次 API 调用处理一个参数类型的所有值
```

### 3. 缓存机制
- 避免重复分析相同的参数组合
- 缓存 LLM 分析结果
- 支持增量更新

## 质量保证机制

### 1. 配置项质量控制
```python
# LLM Prompt 中的质量要求
重要原则：
1. 严格基于提供的参数值进行分析，不可推断或臆断不存在的内容
2. 从参数值的核心差异点归纳配置维度，避免产生重复或相似的配置项
3. 配置项名称必须简洁明确，直接反映参数值的本质差异
4. 候选项应该是互斥的、不重复的，代表不同的配置选择
```

### 2. 结果验证
- 自动化的格式验证
- 语义一致性检查
- 候选项互斥性验证

## 统计分析功能

### `generate_variability_summary(data_with_variability) -> Dict[str, Any]`

**功能**: 生成可变性分析的统计摘要

**统计指标**:
```python
summary = {
    "总体统计": {
        "分析动作数": 0,
        "识别配置项总数": 0,
        "参数类型数": 0
    },
    "动作配置统计": {},  # 每个动作的详细统计
    "配置项分布": {},     # 配置项名称的分布统计
    "详细配置项": {}      # 完整的配置项信息
}
```

## 使用示例

### 单组数据分析
```python
# 分析单个功能组的可变性
result = analyze_requirement_variability(
    standardized_data=action_data,
    api_key=api_key,
    model="deepseek-chat",
    save_results=True
)
```

### 分组数据分析
```python
# 分析所有功能组的可变性
result = run_analyze_requirement_variability(
    clustered_data=all_groups_data,
    api_key=api_key,
    model="deepseek-chat", 
    save_results=True
)
```

### 完整流程示例
```python
# 完整的可变性分析流程
api_key = os.getenv("DEEPSEEK_API_KEY")

# 加载语义角色分析结果
with open("semantic_roles_result.json", 'r') as f:
    semantic_data = json.load(f)

# 执行可变性分析
variability_result = run_analyze_requirement_variability(
    clustered_data=semantic_data,
    api_key=api_key,
    model="deepseek-chat",
    save_results=True
)

# 生成统计摘要
summary = generate_variability_summary(variability_result)
print(f"识别出 {summary['总体统计']['识别配置项总数']} 个配置项")
```

## 输出文件格式

### 主结果文件
- **文件名**: `variability_analysis_{timestamp}.json`
- **位置**: `{OUTPUT_DIR}/comparison_matrices/`

### 摘要报告文件  
- **文件名**: `variability_summary_{timestamp}.json`
- **内容**: 统计摘要和分析报告

### 文件内容示例
```json
{
    "standardized_actions": {
        "飞行数据采集": {
            "variability_config": {
                "ARG1": [
                    {
                        "配置项名称": "数据采集对象",
                        "候选项": ["飞行姿态数据", "速度数据", "位置数据"]
                    }
                ],
                "ARGM-MNR": [
                    {
                        "配置项名称": "采集频率",
                        "候选项": ["实时", "周期性", "触发式"]
                    }
                ]
            }
        }
    }
}
```

## 扩展建议

### 功能扩展
1. **交互式配置**: 支持用户对配置项结果进行交互式调整
2. **配置依赖分析**: 识别配置项之间的依赖关系
3. **配置约束生成**: 自动生成配置约束规则
4. **可变性度量**: 提供可变性复杂度的量化指标

### 性能改进
1. **并行处理**: 支持多个动作的并行分析
2. **增量更新**: 支持新数据的增量分析
3. **缓存优化**: 实现智能缓存减少重复计算
4. **批处理优化**: 优化 API 调用的批处理策略

### 质量提升
1. **人工验证**: 集成人工验证和标注功能
2. **质量评估**: 添加配置项质量的自动评估
3. **一致性检查**: 跨动作的配置项一致性验证
4. **业务验证**: 结合业务规则验证配置合理性
