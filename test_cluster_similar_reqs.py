#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修改后的 cluster_similar_requirements 函数
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from commonReq.cluster_similar_reqs import cluster_similar_requirements
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_cluster_similar_requirements():
    """测试修改后的聚类函数"""
    try:
        logging.info("开始测试修改后的 cluster_similar_requirements 函数...")
        
        # 调用修改后的函数
        topic_model, topics_dict, abstracts = cluster_similar_requirements(
            save_model=False,  # 暂时不保存模型以加快测试
            save_results=True   # 测试新的保存格式
        )
        
        logging.info("测试成功！函数正常工作")
        logging.info(f"处理了 {len(abstracts)} 个摘要")
        logging.info(f"发现了 {len(topics_dict)} 个主题")
        
        # 检查保存的文件是否存在
        from commonReq.config import OUTPUT_DIR
        output_file = os.path.join(OUTPUT_DIR, "commonReq", "topic_clustering_results.json")
        if os.path.exists(output_file):
            logging.info(f"结果文件已成功保存到: {output_file}")
            
            # 读取并验证保存的 JSON 格式
            import json
            with open(output_file, 'r', encoding='utf-8') as f:
                saved_data = json.load(f)
            
            # 验证数据结构
            required_keys = ["metadata", "similar_topic_groups", "all_topic_groups", "standalone_requirements", "failed_requirements"]
            for key in required_keys:
                if key in saved_data:
                    logging.info(f"✓ {key} 存在")
                else:
                    logging.error(f"✗ {key} 缺失")
            
            # 输出元数据信息
            metadata = saved_data.get("metadata", {})
            logging.info(f"元数据: {metadata}")
            
        else:
            logging.error(f"结果文件未找到: {output_file}")
        
        return True
        
    except Exception as e:
        logging.error(f"测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_cluster_similar_requirements()
    sys.exit(0 if success else 1)
