import os
import re
import json
import hashlib
import pdfplumber
from docx import Document
from openai import OpenAI
from commonReq.config import BASE_URL, OUTPUT_DIR, DOCS_DIR


class RequirementExtractor:
    def __init__(self, api_key, model="deepseek-chat", window_size=1000, overlap=200):
        self.model = model
        self.window_size = window_size
        self.overlap = overlap
        self.client = OpenAI(api_key=api_key,base_url=BASE_URL)
        os.makedirs("requirements", exist_ok=True)

    def _read_pdf(self, file_path):
        """提取PDF文本并保留基础格式"""
        text = ""
        with pdfplumber.open(file_path) as pdf:
            for page in pdf.pages:
                page_text = page.extract_text()
                if page_text:
                    # 保留换行符和项目符号
                    text += page_text.replace("\n", "↵") + "\n\n"
        return text.strip()

    def _read_docx(self, file_path):
        """提取DOCX文本并保留段落结构"""
        doc = Document(file_path)
        return "\n\n".join([para.text.replace("\n", "↵") for para in doc.paragraphs if para.text.strip()])

    def load_file(self, file_path):
        """加载PDF或DOCX文件"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        if file_path.lower().endswith('.pdf'):
            return self._read_pdf(file_path)
        elif file_path.lower().endswith(('.docx', '.doc')):
            return self._read_docx(file_path)
        else:
            raise ValueError("不支持的文件格式，仅支持PDF/DOCX")

    def _create_sliding_windows(self, text):
        """创建重叠的文本窗口"""
        if not text or self.window_size <= 0:
            return []
        
        # 确保overlap小于window_size，防止无限循环
        overlap = min(self.overlap, self.window_size - 1)
        
        windows = []
        start = 0
        text_len = len(text)
        
        while start < text_len:
            end = min(start + self.window_size, text_len)
            
            window = text[start:end].strip()
            if window:
                windows.append(window)
            
            # 如果已经到达文本末尾，退出循环
            if end >= text_len:
                break
                
            # 计算下一个窗口的起始位置
            next_start = start + self.window_size - overlap
            
            # 防止无限循环：确保窗口位置前进
            if next_start <= start:
                next_start = start + 1
                
            start = next_start
        
        return windows

    def _call_llm(self, text_chunk):
        """调用LLM提取需求"""
        system_prompt = (
            "你是一个专业的需求分析助手。请从以下文本中提取单个、完整的功能需求。"
            "每个需求通常为 x.x.x.x 的标题处开始，每个需求是具体的，且有完整 字母-数字 ID。"
            "忽略文档中的背景、介绍、附录等非功能性内容，仅提取功能需求。"
            "输出格式为 JSON 数组，每个元素为一个对象，包含："
            "{"
            "  'req_id': '需求 ID',"
            "  'req_name': '需求的简短描述',"
            "  'req_content': '需求的原始文本内容（含标题及正文）'"
            "}"
            "不要添加任何多余的文字或格式。"
        )
        user_prompt = (
            f"从以下文本中提取所有软件需求，只输出JSON数组，不要包含其他任何内容：\n\n"
            f"{text_chunk}"
        )
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1,
                max_tokens=2000
            )
            return response.choices[0].message.content.strip() if response.choices[0].message.content else "[]"
        except Exception as e:
            print(f"LLM调用错误: {str(e)}")
            return "[]"

    def _parse_llm_response(self, response):
        """解析并验证LLM响应"""
        try:
            requirements = json.loads(response)
            if not isinstance(requirements, list):
                return []
            
            validated = []
            for req in requirements:
                # 基础验证
                if not isinstance(req, dict):
                    continue
                if 'req_content' not in req or not req['req_content'].strip():
                    continue
                
                # 清理数据
                req['req_id'] = req.get('req_id', '').strip() or None
                req['req_name'] = req.get('req_name', '').strip() or None
                req['req_content'] = req['req_content'].strip()
                
                validated.append(req)
            return validated
        except json.JSONDecodeError:
            print("JSON解析失败，原始响应:", response)
            return []

    def _generate_jsonl_filename(self, input_file_path):
        """根据输入文档名生成JSONL文件名"""
        # 获取不带扩展名的文件名
        basename = os.path.splitext(os.path.basename(input_file_path))[0]
        # 清理文件名，移除特殊字符
        safe_name = re.sub(r'[^a-zA-Z0-9_-\u4e00-\u9fff]', '_', basename)
        return f"{safe_name}_requirements.jsonl"
    
    def load_requirements_from_jsonl(self, jsonl_path):
        """从JSONL文件加载需求"""
        requirements = []
        try:
            with open(jsonl_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        req = json.loads(line)
                        requirements.append(req)
        except Exception as e:
            print(f"加载JSONL文件失败: {str(e)}")
        return requirements

    def extract_requirements(self, file_path):
        """主处理流程"""
        # 1. 加载文件
        raw_text = self.load_file(file_path)
        print(f"已加载文本，长度: {len(raw_text)}字符")
        
        # 2. 分割窗口
        windows = self._create_sliding_windows(raw_text)
        print(f"创建 {len(windows)} 个文本窗口进行解析")
        
        # 3. 处理每个窗口
        all_requirements = []
        for i, window in enumerate(windows, 1):
            print(f"处理窗口 {i}/{len(windows)} (大小: {len(window)}字符)")
            
            # 4. 调用LLM
            response = self._call_llm(window)
            
            # 5. 解析响应
            requirements = self._parse_llm_response(response)
            print(f"从窗口 {i} 提取到 {len(requirements)} 个需求")
            
            # 6. 合并结果
            all_requirements.extend(requirements)
        
        # 7. 去重处理
        unique_reqs = {}
        for req in all_requirements:
            content = req['req_content']
            # 使用内容哈希作为唯一标识
            content_hash = hashlib.md5(content.encode()).hexdigest()
            if content_hash not in unique_reqs:
                unique_reqs[content_hash] = req
        
        print(f"共提取 {len(all_requirements)} 个需求，去重后: {len(unique_reqs)} 个")
        
        # 8. 确保输出目录存在
        requirements_dir = os.path.join(OUTPUT_DIR, "requirements")
        os.makedirs(requirements_dir, exist_ok=True)
        
        # 9. 保存结果到JSONL文件
        jsonl_filename = self._generate_jsonl_filename(file_path)
        jsonl_path = os.path.join(requirements_dir, jsonl_filename)
        
        with open(jsonl_path, 'w', encoding='utf-8') as f:
            for req in unique_reqs.values():
                # 每行写入一个JSON对象
                json_line = json.dumps(req, ensure_ascii=False)
                f.write(json_line + '\n')
        
        print(f"已保存 {len(unique_reqs)} 个需求到文件: {jsonl_path}")
        
        return len(unique_reqs)

# 使用示例
if __name__ == "__main__":

    api_key = os.getenv("DEEPSEEK_API_KEY")

    # 配置OpenAI API密钥
    extractor = RequirementExtractor(api_key=api_key)
    
    # DOCS_DIR目录下的示例文件
    example_file = os.path.join(DOCS_DIR, "WEA-TH-1-需求文档-1.5.docx")

    # 处理文件
    result_count = extractor.extract_requirements(example_file)
    print(f"成功保存 {result_count} 个需求文件到 {OUTPUT_DIR} 目录")