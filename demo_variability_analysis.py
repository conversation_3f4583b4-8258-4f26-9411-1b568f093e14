"""
需求可变性分析演示
这个脚本演示如何使用generate_comparison_matrix.py中的功能来分析需求可变性
"""

import json
import os
import sys
from pathlib import Path

# 添加commonReq模块到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from commonReq.generate_comparison_matrix import analyze_requirement_variability, generate_variability_summary
from commonReq.config import logging

def load_sample_data():
    """加载示例数据"""
    sample_data = {
        "standardized_actions": {
            "多模态交互输入处理": {
                "steps_with_source": [
                    {
                        "step": "系统接收语音和手势交互输入",
                        "req_id": "需求_30",
                        "original_index": 16,
                        "semantic_tuples": [
                            ["接收", "ARG0", "系统", "ARG1", "语音和手势交互输入"]
                        ]
                    },
                    {
                        "step": "系统接收语音指令、手势操作或AR/VR可视化交互",
                        "req_id": "需求_40",
                        "original_index": 20,
                        "semantic_tuples": [
                            ["接收", "ARG0", "系统", "ARG1", "语音指令、手势操作或AR/VR可视化交互"]
                        ]
                    },
                    {
                        "step": "系统接收语音输入",
                        "req_id": "需求_10",
                        "original_index": 0,
                        "semantic_tuples": [
                            ["接收", "ARG0", "系统", "ARG1", "语音输入"]
                        ]
                    },
                    {
                        "step": "系统解析手势输入",
                        "req_id": "需求_10",
                        "original_index": 1,
                        "semantic_tuples": [
                            ["解析", "ARG0", "系统", "ARG1", "手势输入"]
                        ]
                    },
                    {
                        "step": "系统接收语音指令",
                        "req_id": "需求_20",
                        "original_index": 7,
                        "semantic_tuples": [
                            ["接收", "ARG0", "系统", "ARG1", "语音指令"]
                        ]
                    },
                    {
                        "step": "系统识别手势信号",
                        "req_id": "需求_20",
                        "original_index": 8,
                        "semantic_tuples": [
                            ["识别", "ARG0", "系统", "ARG1", "手势信号"]
                        ]
                    },
                    {
                        "step": "系统提供交互响应",
                        "req_id": "需求_20",
                        "original_index": 15,
                        "semantic_tuples": [
                            ["提供", "ARG0", "系统", "ARG1", "交互响应"]
                        ]
                    }
                ],
                "req_sources": ["需求_10", "需求_40", "需求_20", "需求_30"],
                "step_count": 7,
                "cluster_id": 1
            },
            "数据处理与存储": {
                "steps_with_source": [
                    {
                        "step": "系统将数据存储到本地数据库",
                        "req_id": "需求_10",
                        "original_index": 5,
                        "semantic_tuples": [
                            ["存储", "ARG0", "系统", "ARG1", "数据", "ARG2", "本地数据库"]
                        ]
                    },
                    {
                        "step": "系统将处理结果保存到云端存储",
                        "req_id": "需求_20",
                        "original_index": 12,
                        "semantic_tuples": [
                            ["保存", "ARG0", "系统", "ARG1", "处理结果", "ARG2", "云端存储"]
                        ]
                    },
                    {
                        "step": "系统备份数据到远程服务器",
                        "req_id": "需求_30",
                        "original_index": 18,
                        "semantic_tuples": [
                            ["备份", "ARG0", "系统", "ARG1", "数据", "ARG2", "远程服务器"]
                        ]
                    }
                ],
                "req_sources": ["需求_10", "需求_20", "需求_30"],
                "step_count": 3,
                "cluster_id": 2
            },
            "用户界面显示": {
                "steps_with_source": [
                    {
                        "step": "系统在移动端显示结果",
                        "req_id": "需求_10",
                        "original_index": 6,
                        "semantic_tuples": [
                            ["显示", "ARG0", "系统", "ARG1", "结果", "ARGM-LOC", "移动端"]
                        ]
                    },
                    {
                        "step": "系统通过Web界面展示数据",
                        "req_id": "需求_20",
                        "original_index": 13,
                        "semantic_tuples": [
                            ["展示", "ARG0", "系统", "ARG1", "数据", "ARGM-MNR", "通过Web界面"]
                        ]
                    },
                    {
                        "step": "系统在桌面应用中呈现信息",
                        "req_id": "需求_30",
                        "original_index": 19,
                        "semantic_tuples": [
                            ["呈现", "ARG0", "系统", "ARG1", "信息", "ARGM-LOC", "桌面应用中"]
                        ]
                    }
                ],
                "req_sources": ["需求_10", "需求_20", "需求_30"],
                "step_count": 3,
                "cluster_id": 3
            }
        }
    }
    return sample_data

def demo_variability_analysis():
    """演示可变性分析功能"""
    print("="*60)
    print("需求可变性分析演示")
    print("="*60)
    
    # 加载示例数据
    print("\n1. 加载示例数据...")
    sample_data = load_sample_data()
    
    print(f"   - 加载了 {len(sample_data['standardized_actions'])} 个标准化动作")
    for action_name, action_data in sample_data['standardized_actions'].items():
        step_count = len(action_data['steps_with_source'])
        print(f"     * {action_name}: {step_count} 个步骤")
    
    # 注意: 这里需要您提供真实的API密钥
    api_key = input("\n2. 请输入您的DeepSeek API密钥 (或按Enter跳过实际分析): ").strip()
    
    if not api_key:
        print("\n跳过实际的LLM分析，展示数据处理流程...")
        
        # 展示数据提取和矩阵展开的过程
        from commonReq.generate_comparison_matrix import extract_semantic_tuples, expand_tuples_to_matrix
        
        print("\n3. 提取语义元组...")
        semantic_tuples_by_action = extract_semantic_tuples(sample_data)
        
        for action_name, tuples in semantic_tuples_by_action.items():
            print(f"   - {action_name}: {len(tuples)} 个语义元组")
            for i, tuple_data in enumerate(tuples[:2]):  # 只显示前2个
                print(f"     [{i+1}] {tuple_data}")
            if len(tuples) > 2:
                print(f"     ... 还有 {len(tuples)-2} 个元组")
        
        print("\n4. 展开为参数矩阵...")
        for action_name, tuples in semantic_tuples_by_action.items():
            df = expand_tuples_to_matrix(tuples)
            if not df.empty:
                print(f"   - {action_name}: {df.shape[0]}行 × {df.shape[1]}列")
                print(f"     列名: {list(df.columns)}")
                print(f"     示例数据:")
                for i, (_, row) in enumerate(df.iterrows()):
                    if i < 2:  # 只显示前2行
                        print(f"       行{i+1}: {dict(row)}")
                    if i >= 1:
                        break
                if df.shape[0] > 2:
                    print(f"       ... 还有 {df.shape[0]-2} 行")
            print()
        
        print("演示完成！如需查看完整的LLM分析结果，请提供API密钥重新运行。")
        return
    
    print("\n3. 开始可变性分析...")
    try:
        # 运行可变性分析
        result_data = analyze_requirement_variability(
            sample_data, 
            api_key, 
            model="deepseek-chat", 
            save_results=True
        )
        
        print("\n4. 分析完成！结果概览:")
        
        # 显示分析结果
        for action_name, action_data in result_data['standardized_actions'].items():
            if 'variability_config' in action_data:
                print(f"\n   动作: {action_name}")
                variability_config = action_data['variability_config']
                
                for param_type, config_items in variability_config.items():
                    print(f"     参数类型 {param_type}:")
                    for config_item in config_items:
                        config_name = config_item.get('配置项名称', '')
                        candidates = config_item.get('候选项', [])
                        print(f"       - {config_name}: {candidates}")
        
        print("\n5. 生成摘要报告...")
        summary = generate_variability_summary(result_data)
        
        print(f"\n摘要统计:")
        print(f"   - 分析动作数: {summary['总体统计']['分析动作数']}")
        print(f"   - 识别配置项总数: {summary['总体统计']['识别配置项总数']}")
        print(f"   - 参数类型数: {summary['总体统计']['参数类型数']}")
        
        print(f"\n配置项分布:")
        for config_name, count in summary['配置项分布'].items():
            print(f"   - {config_name}: {count}次")
        
        # 保存摘要
        summary_file = os.path.join("output", "comparison_matrices", "variability_summary.json")
        os.makedirs(os.path.dirname(summary_file), exist_ok=True)
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        print(f"\n摘要已保存到: {summary_file}")
        
        print("\n✅ 可变性分析演示完成！")
        
    except Exception as e:
        print(f"\n❌ 分析过程中出现错误: {e}")
        logging.error(f"可变性分析演示失败: {e}")

def demo_with_real_data():
    """使用真实数据进行演示"""
    print("\n" + "="*60)
    print("使用真实数据进行可变性分析")
    print("="*60)
    
    # 查找最新的语义角色分析结果文件
    semantic_role_files = []
    output_dir = Path("output/commonReq")
    if output_dir.exists():
        for file_path in output_dir.glob("semantic_roles_*.json"):
            semantic_role_files.append(file_path)
    
    if not semantic_role_files:
        print("未找到语义角色分析结果文件。请先运行语义角色分析。")
        return
    
    # 使用最新的文件
    latest_file = max(semantic_role_files, key=lambda p: p.stat().st_mtime)
    print(f"使用文件: {latest_file}")
    
    try:
        with open(latest_file, 'r', encoding='utf-8') as f:
            real_data = json.load(f)
        
        api_key = input("请输入您的DeepSeek API密钥: ").strip()
        if not api_key:
            print("需要API密钥才能进行可变性分析")
            return
        
        print("开始分析真实数据的需求可变性...")
        result_data = analyze_requirement_variability(
            real_data, 
            api_key, 
            model="deepseek-chat", 
            save_results=True
        )
        
        # 生成并显示摘要
        summary = generate_variability_summary(result_data)
        print("\n真实数据分析摘要:")
        print(f"   - 分析动作数: {summary['总体统计']['分析动作数']}")
        print(f"   - 识别配置项总数: {summary['总体统计']['识别配置项总数']}")
        print(f"   - 参数类型数: {summary['总体统计']['参数类型数']}")
        
        print("✅ 真实数据可变性分析完成！")
        
    except Exception as e:
        print(f"❌ 真实数据分析失败: {e}")

if __name__ == "__main__":
    # 设置日志级别为INFO以查看详细过程
    import logging as py_logging
    py_logging.getLogger().setLevel(py_logging.INFO)
    
    # 运行演示
    demo_variability_analysis()
    
    # 询问是否要分析真实数据
    if input("\n是否要分析真实数据？(y/N): ").strip().lower() in ['y', 'yes']:
        demo_with_real_data()
