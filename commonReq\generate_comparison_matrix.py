import json
import os
import time
from typing import Dict, List, Any, Tuple
from collections import defaultdict
import pandas as pd
from openai import OpenAI
from commonReq.config import OUTPUT_DIR, logging, BASE_URL


def extract_semantic_tuples(standardized_data: Dict[str, Any]) -> Dict[str, List[List[Any]]]:
    """
    从standardized_actions中提取所有semantic_tuples
    
    Args:
        standardized_data: 包含standardized_actions的数据字典
        
    Returns:
        dict: key为动作名称，value为该动作下所有的semantic_tuples列表
    """
    logging.info("=== 提取语义元组 ===")
    
    if "standardized_actions" not in standardized_data:
        logging.error("输入数据中缺少 'standardized_actions' 字段")
        return {}
    
    semantic_tuples_by_action = {}
    
    for action_name, action_data in standardized_data["standardized_actions"].items():
        if "steps_with_source" not in action_data:
            logging.warning(f"动作 {action_name} 缺少 steps_with_source 字段")
            continue
            
        all_tuples = []
        for step_info in action_data["steps_with_source"]:
            if "semantic_tuples" in step_info:
                all_tuples.extend(step_info["semantic_tuples"])
        
        semantic_tuples_by_action[action_name] = all_tuples
        logging.info(f"动作 '{action_name}' 提取到 {len(all_tuples)} 个语义元组")
    
    total_tuples = sum(len(tuples) for tuples in semantic_tuples_by_action.values())
    logging.info(f"总共提取到 {total_tuples} 个语义元组")
    
    return semantic_tuples_by_action


def expand_tuples_to_matrix(semantic_tuples: List[List[Any]]) -> pd.DataFrame:
    """
    将语义元组展开为二维数组
    列为不同的参数类型，行为tuple对应的参数
    
    Args:
        semantic_tuples: 语义元组列表，格式为 [[谓语, 参数类型1, 参数内容1, 参数类型2, 参数内容2, ...], ...]
        
    Returns:
        pd.DataFrame: 展开后的二维矩阵
    """
    logging.debug(f"展开 {len(semantic_tuples)} 个语义元组为矩阵")
    
    if not semantic_tuples:
        return pd.DataFrame()
    
    # 收集所有可能的参数类型
    all_arg_types = set()
    for tuple_data in semantic_tuples:
        if len(tuple_data) >= 3:  # 至少包含谓语和一对参数类型-内容
            # 跳过第一个元素（谓语），然后按2为步长取参数类型
            for i in range(1, len(tuple_data), 2):
                if i < len(tuple_data):
                    all_arg_types.add(tuple_data[i])
    
    # 创建列名：谓语 + 所有参数类型
    columns = ["predicate"] + sorted(list(all_arg_types))
    
    # 创建二维矩阵
    matrix_data = []
    for tuple_data in semantic_tuples:
        if not tuple_data:
            continue
            
        row = {}
        # 添加谓语
        row["predicate"] = tuple_data[0] if len(tuple_data) > 0 else ""
        
        # 初始化所有参数类型为空
        for arg_type in all_arg_types:
            row[arg_type] = ""
        
        # 填充参数
        if len(tuple_data) >= 3:
            for i in range(1, len(tuple_data), 2):
                if i + 1 < len(tuple_data):
                    arg_type = tuple_data[i]
                    arg_content = tuple_data[i + 1]
                    if arg_type in row:
                        # 如果已有内容，则合并
                        if row[arg_type]:
                            row[arg_type] += "; " + str(arg_content)
                        else:
                            row[arg_type] = str(arg_content)
        
        matrix_data.append(row)
    
    df = pd.DataFrame(matrix_data, columns=columns)
    logging.debug(f"生成矩阵: {df.shape[0]} 行 × {df.shape[1]} 列")
    
    return df


def compare_parameters_with_llm(df: pd.DataFrame, action_name: str, api_key: str, 
                               model: str = "deepseek-chat", max_retries: int = 3) -> Dict[str, List[Dict[str, Any]]]:
    """
    使用LLM对比参数中的配置项
    忽略谓语和ARG0类型的参数，对比其他类型的参数
    
    Args:
        df: 展开后的参数矩阵
        action_name: 动作名称
        api_key: OpenAI API密钥
        model: 使用的模型名称
        max_retries: 最大重试次数
        
    Returns:
        dict: 配置项分析结果，格式为 {参数类型: [{"配置项名称": "xxx", "候选项": ["选项1", "选项2", ...]}]}
    """
    logging.info(f"开始LLM对比分析: {action_name}")
    
    if df.empty:
        logging.warning(f"动作 {action_name} 的参数矩阵为空")
        return {}
    
    # 初始化OpenAI客户端
    try:
        client = OpenAI(api_key=api_key, base_url=BASE_URL)
    except Exception as e:
        logging.error(f"OpenAI客户端初始化失败: {e}")
        return {}
    
    # 需要对比的参数类型（忽略谓语和ARG0）
    param_types_to_compare = [col for col in df.columns if col not in ["predicate", "ARG0"]]
    
    if not param_types_to_compare:
        logging.info(f"动作 {action_name} 没有需要对比的参数类型")
        return {}
    
    config_items = {}
    
    for param_type in param_types_to_compare:
        # 获取该参数类型的所有值
        param_values = df[param_type].dropna()
        param_values = [str(val).strip() for val in param_values if str(val).strip()]
        
        # 去重但保持顺序
        unique_values = []
        seen = set()
        for val in param_values:
            if val not in seen:
                unique_values.append(val)
                seen.add(val)
        
        # if len(unique_values) < 2:
        #     logging.info(f"参数类型 {param_type} 的唯一值少于2个，跳过对比")
        #     continue
        
        # # 如果值太少或差异不明显，也跳过
        # if len(unique_values) == 2 and len(set([val.lower().strip() for val in unique_values])) == 1:
        #     logging.info(f"参数类型 {param_type} 的值差异不明显，跳过对比")
        #     continue
        
        logging.info(f"对比参数类型 {param_type}，共 {len(unique_values)} 个不同值")
        
        # 构建LLM提示
        system_prompt = """你是一个需求分析专家，负责从给定的参数值中归纳出配置项。

重要原则：
1. 严格基于提供的参数值进行分析，不可推断或臆断不存在的内容
2. 从参数值的核心差异点归纳配置维度，避免产生重复或相似的配置项
3. 配置项名称必须简洁明确，直接反映参数值的本质差异
4. 候选项应该是互斥的、不重复的，代表不同的配置选择
5. 如果参数值有重叠内容，提取其核心区别点作为候选项
6. 每个配置项下的候选项不应该有语义重复

语义角色说明：
- ARG1: 受事者（动作对象）
- ARG2: 起点、来源、材料等
- ARG3: 终点、目标、受益者等
- ARG4: 其他补语
- ARGM-CND: 条件
- ARGM-LOC: 地点
- ARGM-MNR: 方式
- ARGM-TMP: 时间

分析步骤：
1. 识别参数值中的核心关键词和差异点
2. 将相似或重复的概念合并
3. 归纳出清晰的配置维度名称
4. 生成互不重复的候选项列表

返回JSON格式：
[
    {
        "配置项名称": "简洁明确的配置维度名称",
        "候选项": ["选项1", "选项2", "选项3"]
    }
]

注意：候选项之间应该是互斥的，不应该出现语义重复或包含关系。只返回JSON数组。"""

        user_prompt = f"""请基于以下{param_type}类型的参数值进行分析，归纳出配置项：

参数值列表：
{chr(10).join(f"{i+1}. {val}" for i, val in enumerate(unique_values))}

动作名称：{action_name}

分析要求：
- 识别参数值中的核心关键词和本质差异
- 避免生成重复或相似的配置项
- 候选项应该互不重复，代表不同的配置选择
- 如果参数值有共同部分，重点关注其差异部分
- 配置项名称要体现参数的核心区别维度

示例：
如果参数值是["通信链路状态", "通信状态报告", "通信中断情况"]
应归纳为：
- 配置项名称: "通信监控内容"
- 候选项: ["链路状态", "状态报告", "中断情况"]

而不是重复提取"通信"等共同部分。"""

        # 调用LLM进行分析
        config_result = _call_llm_for_config_analysis(client, system_prompt, user_prompt, model, max_retries)
        
        if config_result:
            config_items[param_type] = config_result
            logging.info(f"参数类型 {param_type} 识别出 {len(config_result)} 个配置项")
        else:
            logging.warning(f"参数类型 {param_type} 未识别出配置项")
        
        # 添加延迟避免API限制
        time.sleep(1)
    
    logging.info(f"动作 {action_name} 配置项分析完成，共识别出 {len(config_items)} 个参数类型的配置项")
    return config_items


def _call_llm_for_config_analysis(client: OpenAI, system_prompt: str, user_prompt: str, 
                                 model: str, max_retries: int) -> List[Dict[str, Any]]:
    """
    调用LLM进行配置项分析
    
    Args:
        client: OpenAI客户端
        system_prompt: 系统提示
        user_prompt: 用户提示
        model: 模型名称
        max_retries: 最大重试次数
        
    Returns:
        list: 配置项列表
    """
    for attempt in range(max_retries):
        try:
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1,
                max_tokens=2000,
                timeout=60
            )
            
            content = response.choices[0].message.content
            if not content:
                logging.warning("API返回空内容")
                continue
            
            # 解析JSON响应
            try:
                result = json.loads(content.strip())
                if isinstance(result, list):
                    # 验证结果格式
                    valid_configs = []
                    for item in result:
                        if isinstance(item, dict) and "配置项名称" in item and "候选项" in item:
                            if isinstance(item["候选项"], list) and len(item["候选项"]) > 0:
                                valid_configs.append(item)
                    return valid_configs
                else:
                    logging.warning(f"API返回的不是列表格式: {type(result)}")
                    
            except json.JSONDecodeError:
                # 尝试提取JSON部分
                import re
                json_match = re.search(r'\[.*\]', content.strip(), re.DOTALL)
                if json_match:
                    try:
                        result = json.loads(json_match.group(0))
                        if isinstance(result, list):
                            valid_configs = []
                            for item in result:
                                if isinstance(item, dict) and "配置项名称" in item and "候选项" in item:
                                    if isinstance(item["候选项"], list) and len(item["候选项"]) > 0:
                                        valid_configs.append(item)
                            return valid_configs
                    except json.JSONDecodeError:
                        pass
                        
                logging.warning(f"无法解析JSON响应: {content[:200]}...")
                
        except Exception as e:
            error_msg = str(e).lower()
            if attempt < max_retries - 1:
                wait_time = 2 * (attempt + 1)
                logging.warning(f"API调用失败，{wait_time}秒后重试: {e}")
                time.sleep(wait_time)
            else:
                logging.error(f"API调用达到最大重试次数: {e}")
    
    return []


def analyze_requirement_variability(standardized_data: Dict[str, Any], api_key: str, 
                                  model: str = "deepseek-chat", save_results: bool = True) -> Dict[str, Any]:
    """
    分析需求可变性的主函数
    
    Args:
        standardized_data: 包含standardized_actions的数据字典
        api_key: OpenAI API密钥
        model: 使用的模型名称
        save_results: 是否保存结果
        
    Returns:
        dict: 更新后的数据，包含可变性分析结果
    """
    logging.info("=== 开始需求可变性分析 ===")
    
    # 复制输入数据
    result_data = json.loads(json.dumps(standardized_data))
    
    # 步骤1: 提取所有semantic_tuples
    semantic_tuples_by_action = extract_semantic_tuples(standardized_data)
    
    if not semantic_tuples_by_action:
        logging.error("未找到任何语义元组，无法进行可变性分析")
        return result_data
    
    # 对每个动作进行可变性分析
    for action_name, semantic_tuples in semantic_tuples_by_action.items():
        logging.info(f"\n分析动作: {action_name}")
        
        if not semantic_tuples:
            logging.info(f"动作 {action_name} 没有语义元组，跳过")
            continue
        
        # 步骤2: 展开为二维数组
        df_matrix = expand_tuples_to_matrix(semantic_tuples)
        
        if df_matrix.empty:
            logging.info(f"动作 {action_name} 的参数矩阵为空，跳过")
            continue
        
        logging.info(f"参数矩阵形状: {df_matrix.shape}")
        logging.debug(f"参数矩阵列: {list(df_matrix.columns)}")
        
        # 步骤3: 使用LLM对比参数，生成配置项
        config_items = compare_parameters_with_llm(df_matrix, action_name, api_key, model)
        
        # 步骤4: 将结果保存回原数据结构
        if config_items and "standardized_actions" in result_data:
            if action_name in result_data["standardized_actions"]:
                result_data["standardized_actions"][action_name]["variability_config"] = config_items
                logging.info(f"动作 {action_name} 的配置项分析结果已保存")
            else:
                logging.warning(f"未找到动作 {action_name} 在结果数据中")
        
        # 添加延迟
        time.sleep(0.5)
    
    # 统计结果
    total_configs = 0
    total_config_items = 0
    for action_name, action_data in result_data.get("standardized_actions", {}).items():
        if "variability_config" in action_data:
            total_configs += len(action_data["variability_config"])
            for param_configs in action_data["variability_config"].values():
                total_config_items += len(param_configs)
    
    logging.info(f"\n=== 需求可变性分析完成 ===")
    logging.info(f"总共分析了 {len(semantic_tuples_by_action)} 个动作")
    logging.info(f"识别出 {total_configs} 个参数类型的配置")
    logging.info(f"总共识别出 {total_config_items} 个配置项")
    
    if save_results:
        # 保存结果到JSON文件
        logging.info("保存可变性分析结果...")
        os.makedirs(os.path.join(OUTPUT_DIR, "comparison_matrices"), exist_ok=True)
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(OUTPUT_DIR, "comparison_matrices", f"variability_analysis_{timestamp}.json")
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)
            logging.info(f"可变性分析结果已保存到: {output_file}")
        except Exception as e:
            logging.error(f"保存可变性分析结果失败: {e}")
    
    return result_data


def generate_variability_summary(data_with_variability: Dict[str, Any]) -> Dict[str, Any]:
    """
    生成可变性分析的摘要报告
    
    Args:
        data_with_variability: 包含可变性分析结果的数据
        
    Returns:
        dict: 可变性摘要报告
    """
    logging.info("=== 生成可变性分析摘要 ===")
    
    summary = {
        "总体统计": {
            "分析动作数": 0,
            "识别配置项总数": 0,
            "参数类型数": 0
        },
        "动作配置统计": {},
        "配置项分布": defaultdict(int),
        "详细配置项": {}
    }
    
    standardized_actions = data_with_variability.get("standardized_actions", {})
    
    for action_name, action_data in standardized_actions.items():
        if "variability_config" not in action_data:
            continue
            
        summary["总体统计"]["分析动作数"] += 1
        variability_config = action_data["variability_config"]
        
        action_stats = {
            "参数类型数": len(variability_config),
            "配置项数": 0,
            "配置项详情": {}
        }
        
        for param_type, config_items in variability_config.items():
            summary["总体统计"]["参数类型数"] += 1
            action_stats["配置项数"] += len(config_items)
            summary["总体统计"]["识别配置项总数"] += len(config_items)
            
            param_configs = []
            for config_item in config_items:
                config_name = config_item.get("配置项名称", "")
                candidates = config_item.get("候选项", [])
                param_configs.append({
                    "配置项": config_name,
                    "候选项数": len(candidates),
                    "候选项": candidates
                })
                
                # 统计配置项分布
                summary["配置项分布"][config_name] += 1
            
            action_stats["配置项详情"][param_type] = param_configs
        
        summary["动作配置统计"][action_name] = action_stats
    
    # 转换defaultdict为普通dict
    summary["配置项分布"] = dict(summary["配置项分布"])
    
    logging.info(f"摘要生成完成:")
    logging.info(f"  - 分析动作数: {summary['总体统计']['分析动作数']}")
    logging.info(f"  - 识别配置项总数: {summary['总体统计']['识别配置项总数']}")
    logging.info(f"  - 参数类型数: {summary['总体统计']['参数类型数']}")
    
    return summary


def run_analyze_requirement_variability(clustered_data, 
                                       api_key=None, 
                                       model="deepseek-chat", 
                                       save_results=True):
    """
    运行需求可变性分析
    支持处理分组数据或单组数据结构
    
    Args:
        clustered_data: 包含standardized_actions的数据字典
        api_key: OpenAI API 密钥
        model: 使用的 GPT 模型名称
        save_results: 是否保存结果到文件
        
    Returns:
        dict: 更新后的数据，包含需求可变性分析结果
    """
    logging.info("=== 运行需求可变性分析 ===")
    
    # 检查API密钥
    if not api_key:
        logging.error("需求可变性分析需要API密钥，请提供api_key参数")
        return clustered_data
    
    # 先复制一份，避免修改原始数据
    clustered_data = json.loads(json.dumps(clustered_data))
    
    # 检查数据结构，处理分组数据或单组数据
    if 'decomposed_function_groups' in clustered_data:
        # 处理分组数据结构
        logging.info("检测到分组数据结构，开始逐组分析...")
        total_groups = len(clustered_data['decomposed_function_groups'])
        
        for group_idx, (group_id, req_group) in enumerate(clustered_data['decomposed_function_groups'].items()):
            logging.info(f"处理功能组 {group_id} ({group_idx + 1}/{total_groups})")
            
            if 'standardized_actions' in req_group:
                try:
                    # 对单个功能组进行可变性分析
                    res = analyze_requirement_variability(
                        req_group, 
                        api_key=api_key, 
                        model=model, 
                        save_results=False
                    )
                    clustered_data['decomposed_function_groups'][group_id] = res
                    logging.info(f"功能组 {group_id} 可变性分析完成")
                except Exception as e:
                    logging.error(f"功能组 {group_id} 可变性分析失败: {e}")
                    # 继续处理其他组，不中断整个流程
                    continue
            else:
                logging.warning(f"功能组 {group_id} 中未找到 standardized_actions 字段")
        
        logging.info("所有功能组可变性分析完成")
        
    else:
        # 处理单组数据结构
        logging.info("检测到单组数据结构，开始分析...")
        if 'standardized_actions' in clustered_data:
            try:
                clustered_data = analyze_requirement_variability(
                    clustered_data, 
                    api_key=api_key, 
                    model=model, 
                    save_results=False
                )
                logging.info("单组数据可变性分析完成")
            except Exception as e:
                logging.error(f"单组数据可变性分析失败: {e}")
                return clustered_data
        else:
            logging.warning("数据中未找到 standardized_actions 字段")
            return clustered_data
    
    logging.info("需求可变性分析完成")
    
    # 统计整体结果
    total_actions = 0
    total_configs = 0
    total_config_items = 0
    
    if 'decomposed_function_groups' in clustered_data:
        # 统计分组数据
        for group_id, req_group in clustered_data['decomposed_function_groups'].items():
            if 'standardized_actions' in req_group:
                for action_name, action_data in req_group['standardized_actions'].items():
                    total_actions += 1
                    if 'variability_config' in action_data:
                        total_configs += len(action_data['variability_config'])
                        for param_configs in action_data['variability_config'].values():
                            total_config_items += len(param_configs)
    else:
        # 统计单组数据
        if 'standardized_actions' in clustered_data:
            for action_name, action_data in clustered_data['standardized_actions'].items():
                total_actions += 1
                if 'variability_config' in action_data:
                    total_configs += len(action_data['variability_config'])
                    for param_configs in action_data['variability_config'].values():
                        total_config_items += len(param_configs)
    
    logging.info(f"整体统计:")
    logging.info(f"  - 总动作数: {total_actions}")
    logging.info(f"  - 有配置项的参数类型数: {total_configs}")
    logging.info(f"  - 总配置项数: {total_config_items}")
    
    if save_results:
        # 保存结果到JSON文件
        logging.info("保存需求可变性分析结果...")
        os.makedirs(os.path.join(OUTPUT_DIR, "comparison_matrices"), exist_ok=True)
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(OUTPUT_DIR, "comparison_matrices", f"variability_analysis_{timestamp}.json")
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(clustered_data, f, ensure_ascii=False, indent=2)
            logging.info(f"需求可变性分析结果已保存到: {output_file}")
            
            # 同时生成摘要报告
            if 'decomposed_function_groups' in clustered_data:
                # 为分组数据生成综合摘要
                all_summaries = {}
                for group_id, req_group in clustered_data['decomposed_function_groups'].items():
                    if 'standardized_actions' in req_group:
                        summary = generate_variability_summary(req_group)
                        if summary['总体统计']['分析动作数'] > 0:
                            all_summaries[f"功能组_{group_id}"] = summary
                
                if all_summaries:
                    summary_file = os.path.join(OUTPUT_DIR, "comparison_matrices", f"variability_summary_{timestamp}.json")
                    with open(summary_file, 'w', encoding='utf-8') as f:
                        json.dump(all_summaries, f, ensure_ascii=False, indent=2)
                    logging.info(f"可变性分析摘要已保存到: {summary_file}")
            else:
                # 为单组数据生成摘要
                summary = generate_variability_summary(clustered_data)
                if summary['总体统计']['分析动作数'] > 0:
                    summary_file = os.path.join(OUTPUT_DIR, "comparison_matrices", f"variability_summary_{timestamp}.json")
                    with open(summary_file, 'w', encoding='utf-8') as f:
                        json.dump(summary, f, ensure_ascii=False, indent=2)
                    logging.info(f"可变性分析摘要已保存到: {summary_file}")
            
        except Exception as e:
            logging.error(f"保存需求可变性分析结果失败: {e}")
    else:
        logging.info("...跳过保存需求可变性分析结果")
    
    return clustered_data


