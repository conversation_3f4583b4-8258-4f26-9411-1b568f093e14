#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 HanLP 完整管道的语义角色分析功能
"""

import hanlp
from commonReq.config import logging

def test_hanlp_full_pipeline():
    """测试 HanLP 完整管道"""
    
    try:
        print("正在加载HanLP标准管道...")
        # 尝试使用标准的中文管道
        HanLP = hanlp.load('CLOSE_TOK_POS_NER_SRL_DEP_SDP_CON_ELECTRA_SMALL_ZH')
        print("HanLP管道加载成功")
        
        # 测试句子
        test_sentences = [
            "小明吃了一个苹果",
            "系统处理用户输入", 
            "老师给学生上课",
            "我昨天在图书馆看书",
            "张三把书给了李四"
        ]
        
        for sentence in test_sentences:
            print(f"\n测试句子: {sentence}")
            
            try:
                result = HanLP(sentence)
                print(f"完整分析结果: {result}")
                
                # 提取语义角色标注结果
                if 'srl' in result:
                    srl_result = result['srl']
                    print(f"SRL结果: {srl_result}")
                    
                    if srl_result:
                        for pred_idx, pred_info in enumerate(srl_result):
                            print(f"  谓词 {pred_idx}: {pred_info}")
                else:
                    print("  未找到SRL结果")
                
            except Exception as e:
                print(f"  分析出错: {e}")
                
    except Exception as e:
        print(f"加载HanLP管道失败: {e}")
        print("尝试使用简化的SRL模型...")
        
        try:
            # 尝试使用仅SRL的模型
            srl = hanlp.load('https://file.hankcs.com/hanlp/srl/cpb3_electra_small_crf_has_transform_20220218_135910.zip')
            print("简化SRL模型加载成功")
            
            for sentence in ["小明吃了一个苹果", "张三把书给了李四"]:
                print(f"\n测试句子: {sentence}")
                
                # 尝试不同的调用方式
                try:
                    # 方式1：直接调用
                    result1 = srl(sentence)
                    print(f"方式1结果: {result1}")
                    
                    # 方式2：传入列表
                    result2 = srl([sentence])
                    print(f"方式2结果: {result2}")
                    
                    # 方式3：指定任务
                    result3 = srl([sentence], tasks='srl')
                    print(f"方式3结果: {result3}")
                    
                    # 方式4：指定任务为列表
                    result4 = srl([sentence], tasks=['srl'])
                    print(f"方式4结果: {result4}")
                    
                except Exception as e:
                    print(f"  调用出错: {e}")
                    
        except Exception as e2:
            print(f"加载简化SRL模型也失败: {e2}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_hanlp_full_pipeline()
