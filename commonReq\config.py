import os
import logging

# 动态获取项目根目录路径
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# 定义数据目录路径
DATA_DIR = os.path.join(PROJECT_ROOT, 'data')
MODEL_DIR = os.path.join(PROJECT_ROOT, 'models')
OUTPUT_DIR = os.path.join(PROJECT_ROOT, 'output')
LOG_DIR = os.path.join(PROJECT_ROOT, 'logs')
DOCS_DIR = os.path.join(PROJECT_ROOT, 'docs')
BASE_URL = 'https://api.deepseek.com'

# 创建日志文件
log_file_path = os.path.join("logs", "topic_clustering.log")
file_handler = logging.FileHandler(log_file_path, encoding='utf-8')
logging.basicConfig(
    handlers=[file_handler],
    level=logging.INFO,  # 日志级别
    format="%(asctime)s - %(message)s",  # 日志格式
    datefmt="%Y-%m-%d %H:%M:%S",  # 时间格式
)


# 设置 HanLP 的环境变量 cache 目录
os.environ["HANLP_HOME"] = os.path.join(MODEL_DIR, "hanlp")

# model paths
EMBEDDING_MODEL = os.path.join(MODEL_DIR, "sentence_transformers", "BAAI_bge-base-zh-v1.5")
COMMONREQ_DATA = os.path.join(DATA_DIR, "commonreq_only_abs")

# SRL model, after the HANLP_HOME is set!    
SRL_MODEL = 'CLOSE_TOK_POS_NER_SRL_DEP_SDP_CON_ELECTRA_SMALL_ZH'