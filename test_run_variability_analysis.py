"""
测试 run_analyze_requirement_variability 函数
"""

import json
import sys
from pathlib import Path

# 添加commonReq模块到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from commonReq.generate_comparison_matrix import run_analyze_requirement_variability
from commonReq.config import logging

def test_run_function_with_sample_data():
    """测试 run_analyze_requirement_variability 函数处理示例数据"""
    print("=" * 60)
    print("测试 run_analyze_requirement_variability 函数")
    print("=" * 60)
    
    # 创建单组数据结构的测试数据
    single_group_data = {
        "standardized_actions": {
            "交互处理": {
                "steps_with_source": [
                    {
                        "step": "系统接收语音输入",
                        "req_id": "需求1",
                        "semantic_tuples": [["接收", "ARG0", "系统", "ARG1", "语音输入"]]
                    },
                    {
                        "step": "系统处理手势操作",
                        "req_id": "需求2", 
                        "semantic_tuples": [["处理", "ARG0", "系统", "ARG1", "手势操作"]]
                    }
                ]
            }
        }
    }
    
    # 创建分组数据结构的测试数据
    grouped_data = {
        "metadata": {"test": "data"},
        "decomposed_function_groups": {
            "1": {
                "core_function": "测试功能组1",
                "standardized_actions": {
                    "数据存储": {
                        "steps_with_source": [
                            {
                                "step": "系统保存数据到本地数据库",
                                "req_id": "需求1",
                                "semantic_tuples": [["保存", "ARG0", "系统", "ARG1", "数据", "ARG2", "本地数据库"]]
                            },
                            {
                                "step": "系统备份信息到云端存储",
                                "req_id": "需求2",
                                "semantic_tuples": [["备份", "ARG0", "系统", "ARG1", "信息", "ARG2", "云端存储"]]
                            }
                        ]
                    }
                }
            },
            "2": {
                "core_function": "测试功能组2",
                "standardized_actions": {
                    "用户界面": {
                        "steps_with_source": [
                            {
                                "step": "系统在移动端显示结果",
                                "req_id": "需求3",
                                "semantic_tuples": [["显示", "ARG0", "系统", "ARG1", "结果", "ARGM-LOC", "移动端"]]
                            }
                        ]
                    }
                }
            }
        }
    }
    
    print("\n1. 测试无API密钥的情况...")
    result1 = run_analyze_requirement_variability(single_group_data, api_key=None, save_results=False)
    print("   ✅ 正确处理无API密钥情况")
    
    print("\n2. 测试单组数据结构（模拟，无实际LLM调用）...")
    # 这里不提供真实的API密钥，只测试数据结构处理逻辑
    print("   数据结构检查:")
    print(f"   - 包含standardized_actions: {'standardized_actions' in single_group_data}")
    print(f"   - 动作数量: {len(single_group_data['standardized_actions'])}")
    
    print("\n3. 测试分组数据结构（模拟，无实际LLM调用）...")
    print("   数据结构检查:")
    print(f"   - 包含decomposed_function_groups: {'decomposed_function_groups' in grouped_data}")
    print(f"   - 功能组数量: {len(grouped_data['decomposed_function_groups'])}")
    
    for group_id, group_data in grouped_data['decomposed_function_groups'].items():
        print(f"   - 功能组 {group_id}:")
        print(f"     * 包含standardized_actions: {'standardized_actions' in group_data}")
        if 'standardized_actions' in group_data:
            print(f"     * 动作数量: {len(group_data['standardized_actions'])}")
    
    print("\n4. 测试函数参数验证...")
    
    # 测试默认参数
    print("   - 测试默认参数...")
    try:
        result = run_analyze_requirement_variability(single_group_data, api_key=None, save_results=False)
        print("     ✅ 默认参数处理正常")
    except Exception as e:
        print(f"     ❌ 默认参数处理失败: {e}")
    
    # 测试空数据
    print("   - 测试空数据...")
    empty_data = {"other_field": "value"}
    try:
        result = run_analyze_requirement_variability(empty_data, api_key=None, save_results=False)
        print("     ✅ 空数据处理正常")
    except Exception as e:
        print(f"     ❌ 空数据处理失败: {e}")
    
    print("\n" + "=" * 60)
    print("✅ run_analyze_requirement_variability 函数基础测试完成")
    print("=" * 60)
    
    print("\n如需进行完整的LLM分析测试，请:")
    print("1. 提供DeepSeek API密钥")
    print("2. 运行: result = run_analyze_requirement_variability(data, api_key='your-key')")
    print("3. 或使用 demo_variability_analysis.py 进行交互式测试")

def test_with_real_data():
    """使用真实数据测试函数"""
    print("\n" + "=" * 60)
    print("使用真实数据测试 run_analyze_requirement_variability")
    print("=" * 60)
    
    # 查找最新的语义角色分析结果文件
    real_data_path = Path("output/commonReq")
    if not real_data_path.exists():
        print("未找到真实数据目录")
        return
    
    semantic_files = list(real_data_path.glob("semantic_roles_*.json"))
    if not semantic_files:
        print("未找到语义角色分析文件")
        return
    
    latest_file = max(semantic_files, key=lambda p: p.stat().st_mtime)
    print(f"使用文件: {latest_file}")
    
    try:
        with open(latest_file, 'r', encoding='utf-8') as f:
            real_data = json.load(f)
        
        print("真实数据结构分析:")
        
        if 'decomposed_function_groups' in real_data:
            print("✓ 检测到分组数据结构")
            groups = real_data['decomposed_function_groups']
            print(f"  功能组数量: {len(groups)}")
            
            # 统计动作数量
            total_actions = 0
            for group_id, group_data in groups.items():
                if 'standardized_actions' in group_data:
                    action_count = len(group_data['standardized_actions'])
                    total_actions += action_count
                    print(f"  功能组 {group_id}: {action_count} 个动作")
            
            print(f"  总动作数: {total_actions}")
            
        elif 'standardized_actions' in real_data:
            print("✓ 检测到单组数据结构")
            action_count = len(real_data['standardized_actions'])
            print(f"  动作数量: {action_count}")
        else:
            print("❌ 未识别的数据结构")
            return
        
        # 提示用户是否要进行实际分析
        user_input = input("\n是否要进行实际的可变性分析？需要API密钥 (y/N): ").strip().lower()
        if user_input in ['y', 'yes']:
            api_key = input("请输入DeepSeek API密钥: ").strip()
            if api_key:
                print("开始分析（这可能需要几分钟时间）...")
                try:
                    result = run_analyze_requirement_variability(
                        real_data, 
                        api_key=api_key, 
                        save_results=True
                    )
                    print("✅ 真实数据可变性分析完成！")
                    print("检查 output/comparison_matrices/ 目录查看结果文件")
                except Exception as e:
                    print(f"❌ 分析失败: {e}")
            else:
                print("未提供API密钥，跳过实际分析")
        else:
            print("跳过实际分析")
            
    except Exception as e:
        print(f"❌ 真实数据测试失败: {e}")

if __name__ == "__main__":
    # 设置日志级别
    import logging as py_logging
    py_logging.getLogger().setLevel(py_logging.INFO)
    
    # 运行基础测试
    test_run_function_with_sample_data()
    
    # 询问是否要测试真实数据
    if input("\n是否要使用真实数据测试？(y/N): ").strip().lower() in ['y', 'yes']:
        test_with_real_data()
