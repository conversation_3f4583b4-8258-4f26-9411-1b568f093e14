import os
import json
import re
from openai import OpenAI
import time
from tqdm import tqdm
from commonReq.config import BASE_URL, OUTPUT_DIR

class RequirementDecomposer:
    def __init__(self, api_key, model="deepseek-chat", max_retries=3, delay=2):
        self.model = model
        self.max_retries = max_retries
        self.delay = delay
        self.client = OpenAI(api_key=api_key,base_url=BASE_URL)
        self.output_dir = os.path.join(OUTPUT_DIR, "decomposed_requirements")
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 定义处理状态文件
        self.status_file = os.path.join(self.output_dir, "processing_status.json")
        self.processed_files = self._load_processing_status()
    
    def _load_processing_status(self):
        """加载已处理文件的状态"""
        if os.path.exists(self.status_file):
            try:
                with open(self.status_file, 'r') as f:
                    return json.load(f)
            except:
                return {}
        return {}
    
    def _save_processing_status(self):
        """保存处理状态"""
        with open(self.status_file, 'w') as f:
            json.dump(self.processed_files, f)
    
    def _get_llm_response(self, prompt, system_prompt):
        """调用LLM并处理重试逻辑"""
        for attempt in range(self.max_retries):
            try:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": prompt}
                    ],
                    temperature=0.3,  # 稍高的温度以鼓励推理
                    max_tokens=1500,
                    timeout=30
                )
                return response.choices[0].message.content.strip() if response.choices[0].message.content else ""
            except Exception as e:
                error_msg = str(e).lower()
                if "rate limit" in error_msg:
                    wait = self.delay * (2 ** attempt)
                    print(f"速率限制，等待 {wait} 秒后重试...")
                    time.sleep(wait)
                elif "api" in error_msg or "service" in error_msg:
                    wait = self.delay * (attempt + 1)
                    print(f"API错误，等待 {wait} 秒后重试...")
                    time.sleep(wait)
                else:
                    print(f"LLM调用错误: {str(e)}")
                    if attempt < self.max_retries - 1:
                        time.sleep(self.delay)
                    else:
                        return None
        return None
    
    def _parse_decomposition(self, response_text):
        """解析LLM的拆解响应"""
        # 尝试直接解析JSON
        try:
            return json.loads(response_text)
        except json.JSONDecodeError:
            pass
        
        # 尝试提取JSON部分
        try:
            json_match = re.search(r'\{[\s\S]*\}', response_text)
            if json_match:
                return json.loads(json_match.group(0))
        except:
            pass
        
        # 尝试修复常见问题
        try:
            # 替换单引号为双引号
            fixed_json = re.sub(r"'(.*?)'", r'"\1"', response_text)
            # 修复末尾缺失的括号
            if not fixed_json.strip().endswith('}'):
                fixed_json += '}'
            return json.loads(fixed_json)
        except:
            pass
        
        print(f"无法解析响应: {response_text[:300]}...")
        return None
    
    def decompose_requirement(self, req_data):
        """拆解单个需求"""
        system_prompt = (
            "你是一个专业的需求分析师，负责将软件需求拆解为结构化组件。"
            "请将以下需求拆解为三个部分：\n"
            "1. 输入(Input): 系统接收的数据或触发事件（列表形式）\n"
            "2. 输出(Output): 系统产生的数据或响应（列表形式）\n"
            "3. 需求动作序列(Actions): 系统完成该需求时需要进行的动作\n"
            "输出必须是严格的JSON格式，包含以下字段：\n"
            "{\n"
            "  \"input\": [\"输入项1\", \"输入项2\", ...],\n"
            "  \"output\": [\"输出项1\", \"输出项2\", ...],\n"
            "  \"actions\": [\"动作1\", \"动作2\", ...\n"
            "}\n\n"
            "拆解指南：\n"
            "- 动作序列应该能完整表达功能原意\n"
            "- 将需求描述中的流程拆分为多个动作, 以“系统”为主语\n"
            "- 识别明确提到的输入/输出\n"
            "- 保持原始需求的意图不变\n"
            "- 如果某个部分不适用，使用空列表[]\n"
            "只输出JSON，不要包含其他任何内容。"
        )
        
        user_prompt = (
            f"需求ID: {req_data.get('req_id', '无')}\n"
            f"需求名称: {req_data.get('req_name', '无')}\n"
            f"需求内容:\n{req_data['req_content']}\n\n"
            "请按上述要求拆解此需求:"
        )
        
        response = self._get_llm_response(user_prompt, system_prompt)
        if not response:
            return None
        
        decomposition = self._parse_decomposition(response)
        if not decomposition:
            return None
        
        # 验证结构
        required_keys = ["input", "output", "actions"]
        if not all(key in decomposition for key in required_keys):
            print(f"响应缺少必要字段: {list(decomposition.keys())}")
            return None
        
        # 合并结果
        decomposed_req = {
            "req_id": req_data.get("req_id"),
            "req_name": req_data.get("req_name"),
            "req_content": req_data["req_content"],
            "input": decomposition["input"],
            "output": decomposition["output"],
            "processing_steps": decomposition["actions"],
        }
        
        return decomposed_req
    
    def process_requirements_directory(self, input_dir="requirements", batch_size=10):
        """处理目录中的所有需求文件"""
        # 获取所有需求文件
        req_files = [f for f in os.listdir(input_dir) if f.endswith('.json')]
        print(f"找到 {len(req_files)} 个需求文件")
        
        # 过滤已处理文件
        to_process = [
            f for f in req_files 
            if f not in self.processed_files or 
            self.processed_files.get(f) != "completed"
        ]
        
        if not to_process:
            print("所有需求已处理完成")
            return
        
        print(f"需要处理 {len(to_process)} 个新需求")
        
        # 分批处理
        for i in tqdm(range(len(to_process)), desc="处理需求", unit="req"):
            filename = to_process[i]
            filepath = os.path.join(input_dir, filename)
            
            try:
                # 读取需求文件
                with open(filepath, 'r', encoding='utf8') as f:
                    req_data = json.load(f)
                
                # 拆解需求
                decomposed = self.decompose_requirement(req_data)
                
                if decomposed:
                    # 保存结果
                    output_path = os.path.join(self.output_dir, filename)
                    with open(output_path, 'w', encoding='utf8') as f:
                        json.dump(decomposed, f, indent=2, ensure_ascii=False)
                    
                    # 更新状态
                    self.processed_files[filename] = "completed"
                else:
                    self.processed_files[filename] = "failed"
                    print(f"拆解失败: {filename}")
                
                # 定期保存状态
                if i % 10 == 0:
                    self._save_processing_status()
            
            except Exception as e:
                self.processed_files[filename] = f"error: {str(e)}"
                print(f"处理 {filename} 时出错: {str(e)}")
        
        # 保存最终状态
        self._save_processing_status()
        print(f"处理完成! 结果保存在 {self.output_dir} 目录")

    def process_requirements_from_cluster(self, cluster_file_path, output_file_name=None):
        """
        处理聚类结果文件中的需求，对每个group中的每个需求进行分解
        
        Args:
            cluster_file_path (str): 聚类结果JSON文件路径
            output_file_name (str, optional): 输出文件名，如果不指定则自动生成
        
        Returns:
            dict: 处理后的结果，包含分解后的需求
        """
        print(f"=== 开始处理聚类结果文件: {cluster_file_path} ===")
        
        # 读取聚类结果文件
        try:
            with open(cluster_file_path, 'r', encoding='utf-8') as f:
                cluster_data = json.load(f)
            print(f"成功加载聚类数据")
        except Exception as e:
            print(f"无法读取聚类文件: {e}")
            return None
        
        # 初始化结果结构
        result = {
            "metadata": cluster_data.get("metadata", {}),
            "decomposed_function_groups": {},
            "processing_summary": {
                "total_groups": 0,
                "total_requirements": 0,
                "successfully_decomposed": 0,
                "failed_decompositions": 0,
                "processing_time": time.strftime("%Y-%m-%d %H:%M:%S")
            },
            "standalone_requirements": cluster_data.get("standalone_requirements", [])
        }
        
        # 获取要处理的功能组
        same_function_groups = cluster_data.get("similar_topic_groups", {})
        all_function_groups = cluster_data.get("all_topic_groups", {})
        
        # 优先处理相同功能组，如果不存在则处理所有功能组
        groups_to_process = same_function_groups if same_function_groups else all_function_groups
        
        result["processing_summary"]["total_groups"] = len(groups_to_process)
        
        # 计算总需求数
        total_reqs = sum(len(group["requirements"]) for group in groups_to_process.values())
        result["processing_summary"]["total_requirements"] = total_reqs
        
        print(f"发现 {len(groups_to_process)} 个功能组，共 {total_reqs} 个需求")
        
        # 遍历每个功能组
        for group_id, group_info in tqdm(groups_to_process.items(), desc="处理功能组"):
            print(f"\n--- 处理功能组 {group_id}: {group_info['core_function']} ---")
            
            # 初始化组结果
            group_result = {
                "core_function": group_info["core_function"],
                "original_requirements": group_info["requirements"],
                "decomposed_requirements": [],
                "group_stats": {
                    "total_requirements": len(group_info["requirements"]),
                    "successful_decompositions": 0,
                    "failed_decompositions": 0
                }
            }
            
            # 遍历组内每个需求
            for req_idx, requirement in enumerate(group_info["requirements"], 1):
                print(f"  处理需求 {req_idx}/{len(group_info['requirements'])}: {requirement.get('req_id', f'req_{req_idx}')}")
                
                # 准备需求数据格式
                req_data = {
                    "req_id": requirement.get("req_id", f"group_{group_id}_req_{req_idx}"),
                    'req_name': requirement.get("req_name", f"功能组{group_id}-需求{req_idx}"),
                    "req_content": requirement.get("req_content", requirement.get("text", "")),
                    "source": requirement.get("source", ""),
                    "group_id": str(group_id),
                    "core_function": group_info["core_function"]
                }
                
                # 调用分解函数
                try:
                    decomposed = self.decompose_requirement(req_data)
                    if decomposed:
                        # 添加分组信息到分解结果
                        decomposed["group_id"] = str(group_id)
                        decomposed["core_function"] = group_info["core_function"]
                        decomposed["original_source"] = requirement.get("source", "")
                        
                        group_result["decomposed_requirements"].append(decomposed)
                        group_result["group_stats"]["successful_decompositions"] += 1
                        result["processing_summary"]["successfully_decomposed"] += 1
                        print(f"    ✓ 分解成功")
                    else:
                        print(f"    ✗ 分解失败")
                        group_result["group_stats"]["failed_decompositions"] += 1
                        result["processing_summary"]["failed_decompositions"] += 1
                        
                        # 保存失败的需求信息
                        failed_req = req_data.copy()
                        failed_req["decomposition_status"] = "failed"
                        failed_req["input"] = []
                        failed_req["output"] = []
                        failed_req["processing_steps"] = []
                        group_result["decomposed_requirements"].append(failed_req)
                        
                except Exception as e:
                    print(f"    ✗ 处理异常: {str(e)}")
                    group_result["group_stats"]["failed_decompositions"] += 1
                    result["processing_summary"]["failed_decompositions"] += 1
                    
                    # 保存异常的需求信息
                    failed_req = req_data.copy()
                    failed_req["decomposition_status"] = f"error: {str(e)}"
                    failed_req["input"] = []
                    failed_req["output"] = []
                    failed_req["processing_steps"] = []
                    group_result["decomposed_requirements"].append(failed_req)
                
                # 添加延迟避免API限制
                time.sleep(0.5)
            
            # 保存组结果
            result["decomposed_function_groups"][str(group_id)] = group_result
            
            # 输出组统计
            print(f"  组 {group_id} 处理完成: {group_result['group_stats']['successful_decompositions']}/{group_result['group_stats']['total_requirements']} 成功")
        
        # 保存处理结果
        if output_file_name is None:
            # 从输入文件名生成输出文件名
            base_name = os.path.splitext(os.path.basename(cluster_file_path))[0]
            output_file_name = f"decomposed_{base_name}.json"
        
        output_path = os.path.join(self.output_dir, output_file_name)
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            print(f"\n=== 处理完成! 结果已保存到: {output_path} ===")
        except Exception as e:
            print(f"保存结果失败: {e}")
            return result
        
        # 输出最终统计
        summary = result["processing_summary"]
        print(f"\n处理统计:")
        print(f"  总功能组: {summary['total_groups']}")
        print(f"  总需求数: {summary['total_requirements']}")
        print(f"  成功分解: {summary['successfully_decomposed']}")
        print(f"  分解失败: {summary['failed_decompositions']}")
        print(f"  成功率: {(summary['successfully_decomposed'] / summary['total_requirements'] * 100):.1f}%")
        
        return result

# 使用示例
if __name__ == "__main__":
    # 配置API密钥
    decomposer = RequirementDecomposer(
        api_key="your_openai_api_key_here",
        model="deepseek-chat"
    )
    
    # 示例1: 处理普通需求目录
    # decomposer.process_requirements_directory()
    
    # 示例2: 处理聚类结果文件
    cluster_file = "output/commonReq/llm_same_function_requirements.json"
    if os.path.exists(cluster_file):
        result = decomposer.process_requirements_from_cluster(cluster_file)
        if result:
            print("聚类需求分解完成!")
    else:
        print(f"聚类文件不存在: {cluster_file}")
        print("请先运行聚类分析或提供正确的文件路径")