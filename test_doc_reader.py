#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JSONL输出功能的脚本
"""

import os
import json
from commonReq.document_reader import RequirementExtractor
from commonReq.config import DOCS_DIR, OUTPUT_DIR

def test_jsonl_output():
    """测试JSONL输出功能"""
    
    # 获取API密钥
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("请设置DEEPSEEK_API_KEY环境变量")
        return
    
    # 创建需求提取器
    extractor = RequirementExtractor(api_key=api_key)
    
    # 测试文件路径
    test_file = os.path.join(DOCS_DIR, "WEA-TH-1-需求文档-1.5.docx")
    
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        print("请确保docs目录下有测试文件")
        return
    
    print(f"开始处理文件: {test_file}")
    
    # 提取需求并保存为JSONL
    result_count = extractor.extract_requirements(test_file)
    
    # 生成预期的JSONL文件路径
    jsonl_filename = extractor._generate_jsonl_filename(test_file)
    jsonl_path = os.path.join(OUTPUT_DIR, "requirements", jsonl_filename)
    
    print(f"预期输出文件: {jsonl_path}")
    
    # 验证文件是否存在
    if os.path.exists(jsonl_path):
        print(f"✓ JSONL文件已成功创建")
        
        # 读取并验证内容
        requirements = extractor.load_requirements_from_jsonl(jsonl_path)
        print(f"✓ 从JSONL文件读取到 {len(requirements)} 个需求")
        
        # 显示前几个需求的摘要
        for i, req in enumerate(requirements[:3], 1):
            print(f"\n需求 {i}:")
            print(f"  ID: {req.get('req_id', 'N/A')}")
            print(f"  名称: {req.get('req_name', 'N/A')}")
            print(f"  内容长度: {len(req.get('req_content', ''))}")
            
    else:
        print(f"✗ JSONL文件未找到: {jsonl_path}")

if __name__ == "__main__":
    test_jsonl_output()
