# Requirement Decomposer 模块设计文档

## 概述

`requirement_decomposer.py` 模块提供了需求分解功能，能够将复杂的软件需求自动拆解为结构化的组件，包括输入、输出和处理步骤。该模块基于大语言模型（LLM）的智能分析能力，支持单个需求处理和批量需求分解，为后续的需求分析流程提供标准化的数据基础。

## 功能特性

### 1. 智能需求分解
- 基于 LLM 的语义理解和分析
- 自动识别需求的输入、输出和处理步骤
- 保持原始需求意图的完整性

### 2. 多种处理模式
- **单需求处理**: 针对单个需求的精确分解
- **目录批处理**: 批量处理需求文件目录
- **聚类结果处理**: 专门处理聚类分析的结果文件

### 3. 可靠性保障
- 智能重试机制应对 API 限制
- 处理状态跟踪和断点续传
- 多层次的错误处理和恢复

## 核心类设计

### RequirementDecomposer 类

#### 初始化参数
```python
def __init__(self, api_key, model="deepseek-chat", max_retries=3, delay=2):
    self.model = model              # LLM 模型名称
    self.max_retries = max_retries  # 最大重试次数
    self.delay = delay              # 重试延迟时间
    self.client = OpenAI(...)       # OpenAI 客户端
    self.output_dir = ...           # 输出目录
    self.processed_files = {}       # 处理状态跟踪
```

**设计特点**:
- 支持多种 LLM 模型切换
- 可配置的重试策略
- 自动化的状态管理

## 核心方法设计

### 1. LLM 交互方法

#### `_get_llm_response(self, prompt, system_prompt)`

**功能**: 调用 LLM 并处理重试逻辑

**重试策略**:
```python
for attempt in range(self.max_retries):
    try:
        response = self.client.chat.completions.create(...)
        return response.choices[0].message.content.strip()
    except Exception as e:
        if "rate limit" in str(e).lower():
            wait = self.delay * (2 ** attempt)  # 指数退避
        elif "api" in str(e).lower():
            wait = self.delay * (attempt + 1)   # 线性增加
        else:
            # 通用错误处理
```

**错误处理类型**:
- **速率限制**: 指数退避重试策略
- **API 服务错误**: 线性增加延迟重试  
- **网络超时**: 标准延迟重试
- **其他错误**: 记录并继续

### 2. 响应解析方法

#### `_parse_decomposition(self, response_text)`

**功能**: 解析 LLM 的分解响应，支持多种格式修复

**解析策略**:
```python
# 1. 尝试直接JSON解析
try:
    return json.loads(response_text)
except json.JSONDecodeError:
    pass

# 2. 尝试提取JSON部分
json_match = re.search(r'\{[\s\S]*\}', response_text)
if json_match:
    return json.loads(json_match.group(0))

# 3. 尝试修复常见问题
fixed_json = re.sub(r"'(.*?)'", r'"\1"', response_text)  # 单引号转双引号
if not fixed_json.strip().endswith('}'):
    fixed_json += '}'  # 修复缺失括号
```

**容错机制**:
- 处理不规范的 JSON 格式
- 修复常见的语法错误
- 提取嵌入在文本中的 JSON 片段

### 3. 需求分解方法

#### `decompose_requirement(self, req_data)`

**功能**: 拆解单个需求为结构化组件

**Prompt 设计**:
```python
system_prompt = (
    "你是一个专业的需求分析师，负责将软件需求拆解为结构化组件。"
    "请将以下需求拆解为三个部分：\n"
    "1. 输入(Input): 系统接收的数据或触发事件（列表形式）\n"
    "2. 输出(Output): 系统产生的数据或响应（列表形式）\n"  
    "3. 需求动作序列(Actions): 系统完成该需求时需要进行的动作\n"
    "输出必须是严格的JSON格式：\n"
    "{\n"
    "  \"input\": [\"输入项1\", \"输入项2\", ...],\n"
    "  \"output\": [\"输出项1\", \"输出项2\", ...],\n"
    "  \"actions\": [\"动作1\", \"动作2\", ...]\n"
    "}\n\n"
    "拆解指南：\n"
    "- 动作序列应该能完整表达功能原意\n"
    "- 将需求描述中的流程拆分为多个动作, 以"系统"为主语\n"
    "- 识别明确提到的输入/输出\n"
    "- 保持原始需求的意图不变\n"
    "只输出JSON，不要包含其他任何内容。"
)
```

**分解原则**:
- **完整性**: 保持原始需求意图不变
- **结构化**: 明确区分输入、输出、处理步骤
- **标准化**: 动作以"系统"为主语描述
- **可操作**: 处理步骤具体且可执行

**结果验证**:
```python
# 验证必要字段
required_keys = ["input", "output", "actions"]
if not all(key in decomposition for key in required_keys):
    return None

# 构建标准化结果
decomposed_req = {
    "req_id": req_data.get("req_id"),
    "req_name": req_data.get("req_name"),
    "req_content": req_data["req_content"], 
    "input": decomposition["input"],
    "output": decomposition["output"],
    "processing_steps": decomposition["actions"]  # 统一命名
}
```

## 处理模式设计

### 1. 目录批处理模式

#### `process_requirements_directory(self, input_dir="requirements", batch_size=10)`

**功能**: 处理目录中的所有需求文件

**处理流程**:
```python
# 1. 文件发现和过滤
req_files = [f for f in os.listdir(input_dir) if f.endswith('.json')]
to_process = [f for f in req_files if not self._is_processed(f)]

# 2. 批量处理
for filename in tqdm(to_process, desc="处理需求"):
    # 读取需求
    req_data = json.load(open(filepath))
    
    # 分解需求
    decomposed = self.decompose_requirement(req_data)
    
    # 保存结果
    if decomposed:
        self._save_result(decomposed, filename)
        self.processed_files[filename] = "completed"
    
    # 定期保存状态
    if i % 10 == 0:
        self._save_processing_status()
```

**状态管理**:
- 处理状态持久化存储
- 支持断点续传功能
- 失败需求的详细记录

### 2. 聚类结果处理模式

#### `process_requirements_from_cluster(self, cluster_file_path, output_file_name=None)`

**功能**: 处理聚类结果文件中的需求

**输入格式**:
```python
cluster_data = {
    "metadata": {...},
    "similar_topic_groups": {  # 或 "all_topic_groups"
        "group_1": {
            "core_function": "无人机飞行控制", 
            "requirements": [
                {
                    "req_id": "需求_1",
                    "req_content": "需求文本内容",
                    "source": "系统A"
                }
            ]
        }
    }
}
```

**处理架构**:
```python
# 初始化结果结构
result = {
    "metadata": cluster_data.get("metadata", {}),
    "decomposed_function_groups": {},
    "processing_summary": {
        "total_groups": 0,
        "total_requirements": 0,
        "successfully_decomposed": 0,
        "failed_decompositions": 0
    }
}

# 逐组处理
for group_id, group_info in groups_to_process.items():
    group_result = {
        "core_function": group_info["core_function"],
        "original_requirements": group_info["requirements"],
        "decomposed_requirements": [],
        "group_stats": {...}
    }
    
    # 逐个分解需求
    for requirement in group_info["requirements"]:
        decomposed = self.decompose_requirement(req_data)
        if decomposed:
            decomposed.update({
                "group_id": str(group_id),
                "core_function": group_info["core_function"],
                "original_source": requirement.get("source", "")
            })
            group_result["decomposed_requirements"].append(decomposed)
```

**增强特性**:
- **分组保持**: 维护原有的功能分组结构
- **溯源信息**: 保留完整的来源追踪信息
- **统计报告**: 提供详细的处理统计数据
- **错误容忍**: 单个失败不影响整体处理

## 状态管理设计

### 处理状态跟踪
```python
# 状态文件结构
{
    "file1.json": "completed",
    "file2.json": "failed", 
    "file3.json": "error: API调用失败"
}
```

### 状态持久化
```python
def _load_processing_status(self):
    """加载已处理文件的状态"""
    if os.path.exists(self.status_file):
        with open(self.status_file, 'r') as f:
            return json.load(f)
    return {}

def _save_processing_status(self):
    """保存处理状态"""
    with open(self.status_file, 'w') as f:
        json.dump(self.processed_files, f)
```

## 数据结构设计

### 输入数据结构
```python
# 单个需求格式
{
    "req_id": "需求唯一标识",
    "req_name": "需求名称",
    "req_content": "需求具体内容"
}
```

### 输出数据结构
```python
# 分解后的需求格式
{
    "req_id": "需求_1",
    "req_name": "功能需求名称", 
    "req_content": "原始需求内容",
    "input": ["输入项1", "输入项2"],
    "output": ["输出项1", "输出项2"],
    "processing_steps": ["系统动作1", "系统动作2"],
    
    # 聚类处理时的额外字段
    "group_id": "1",
    "core_function": "核心功能描述",
    "original_source": "来源系统"
}
```

### 批处理结果结构
```python
{
    "metadata": {...},
    "decomposed_function_groups": {
        "group_1": {
            "core_function": "功能描述",
            "original_requirements": [...],
            "decomposed_requirements": [...],
            "group_stats": {
                "total_requirements": 5,
                "successful_decompositions": 4,
                "failed_decompositions": 1
            }
        }
    },
    "processing_summary": {
        "total_groups": 3,
        "total_requirements": 15,
        "successfully_decomposed": 13,
        "failed_decompositions": 2,
        "success_rate": 86.7
    }
}
```

## 错误处理机制

### 多层错误处理
```python
# API调用层错误处理
try:
    response = self._get_llm_response(user_prompt, system_prompt)
except Exception as e:
    logging.error(f"API调用失败: {e}")
    return None

# JSON解析层错误处理  
try:
    decomposition = self._parse_decomposition(response)
except Exception as e:
    logging.error(f"响应解析失败: {e}")
    return None

# 业务逻辑层错误处理
if not self._validate_decomposition(decomposition):
    logging.error("分解结果验证失败")
    return None
```

### 错误分类和处理
1. **API 相关错误**:
   - 速率限制 → 指数退避重试
   - 服务异常 → 线性延迟重试
   - 认证错误 → 立即失败并报告

2. **数据处理错误**:
   - JSON 格式错误 → 尝试修复和提取
   - 字段缺失 → 使用默认值或跳过
   - 内容异常 → 记录并标记失败

3. **文件系统错误**:
   - 文件不存在 → 跳过并记录
   - 权限问题 → 报告并终止
   - 存储空间不足 → 清理并重试

## 性能优化策略

### 1. API 调用优化
```python
# 智能延迟控制
time.sleep(0.5)  # 基础延迟避免过快调用

# 批量状态保存
if i % 10 == 0:  # 每10个需求保存一次状态
    self._save_processing_status()
```

### 2. 内存管理
- 流式处理文件避免内存溢出
- 及时释放大型数据对象
- 分批处理减少内存峰值

### 3. 并发控制
- 单线程顺序处理保证稳定性
- 避免并发 API 调用触发限制
- 合理的处理间隔控制

## 配置依赖

### 外部配置
```python
from commonReq.config import BASE_URL, OUTPUT_DIR
```

### 环境变量
- `DEEPSEEK_API_KEY`: DeepSeek API 密钥
- 或其他 OpenAI 兼容的 API 密钥

### Python 依赖
```python
# API交互
openai                 # OpenAI API客户端

# 进度显示
tqdm                   # 进度条显示

# 标准库
json, os, re, time
```

## 使用示例

### 基本使用
```python
# 初始化分解器
decomposer = RequirementDecomposer(
    api_key="your_api_key",
    model="deepseek-chat",
    max_retries=3
)

# 处理单个需求
req_data = {
    "req_id": "REQ_001", 
    "req_content": "系统应该能够实时监控设备状态..."
}
result = decomposer.decompose_requirement(req_data)
```

### 目录批处理
```python
# 处理需求目录
decomposer.process_requirements_directory(
    input_dir="requirements",
    batch_size=10
)
```

### 聚类结果处理
```python
# 处理聚类分析结果
cluster_file = "output/commonReq/topic_clustering_results.json"
result = decomposer.process_requirements_from_cluster(
    cluster_file_path=cluster_file,
    output_file_name="decomposed_requirements.json"
)
```

### 完整工作流示例
```python
import os
from commonReq.requirement_decomposer import RequirementDecomposer

# 配置
api_key = os.getenv("DEEPSEEK_API_KEY")
decomposer = RequirementDecomposer(api_key=api_key)

# 处理聚类结果
cluster_result = "output/commonReq/similar_topic_groups.json" 
if os.path.exists(cluster_result):
    print("开始分解聚类需求...")
    result = decomposer.process_requirements_from_cluster(cluster_result)
    
    if result:
        print(f"处理完成!")
        print(f"总需求: {result['processing_summary']['total_requirements']}")
        print(f"成功分解: {result['processing_summary']['successfully_decomposed']}")
        print(f"成功率: {result['processing_summary']['successfully_decomposed']/result['processing_summary']['total_requirements']*100:.1f}%")
else:
    print("聚类结果文件不存在，请先运行聚类分析")
```

## 输出文件格式

### 单需求输出
- **位置**: `{OUTPUT_DIR}/decomposed_requirements/`
- **格式**: 与输入文件同名的 JSON 文件

### 批处理输出
- **文件名**: `decomposed_{原始文件名}.json`
- **内容**: 完整的分组和统计信息

### 状态文件
- **文件名**: `processing_status.json` 
- **位置**: `{OUTPUT_DIR}/decomposed_requirements/`
- **用途**: 断点续传和状态跟踪

## 质量保证机制

### 输入验证
- 必需字段存在性检查
- 内容长度合理性验证
- 格式规范性检查

### 输出验证
- JSON 结构完整性验证
- 必要字段存在性检查
- 数据类型正确性验证

### 业务逻辑验证
- 分解结果的合理性检查
- 与原始需求的一致性验证
- 处理步骤的完整性检查

## 扩展建议

### 功能扩展
1. **模板支持**: 支持不同类型需求的分解模板
2. **交互模式**: 支持用户交互式的分解过程
3. **质量评估**: 添加分解质量的自动评估
4. **增量更新**: 支持基于新需求的增量分解

### 性能改进
1. **并行处理**: 在保证API限制的前提下实现并行处理
2. **缓存机制**: 实现相似需求的分解结果缓存
3. **批处理优化**: 优化大批量需求的处理策略
4. **预测优化**: 基于历史数据预测处理时间

### 工具集成
1. **可视化界面**: 提供需求分解的可视化界面
2. **编辑工具**: 开发分解结果的交互式编辑工具
3. **API 封装**: 提供 RESTful API 服务
4. **集成插件**: 开发与其他需求工具的集成插件
