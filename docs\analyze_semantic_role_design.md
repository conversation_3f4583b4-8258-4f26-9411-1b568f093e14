# Analyze Semantic Role 模块设计文档

## 概述

`analyze_semantic_role.py` 模块提供了语义角色标注（Semantic Role Labeling, SRL）功能，用于识别和标注需求处理步骤中的语义角色。该模块支持两种分析方式：基于 HanLP 的传统 NLP 方法和基于大语言模型（LLM）的智能分析方法。

## 功能特性

### 1. 双模式语义分析
- **HanLP 模式**: 使用 HanLP 的预训练语义角色标注模型
- **LLM 模式**: 基于 OpenAI GPT 模型的智能语义分析
- 支持根据需求选择合适的分析方法

### 2. 标准语义角色支持
- 支持完整的语义角色体系
- 自动识别谓词-论元结构
- 生成结构化的语义元组

### 3. 批量处理能力
- 支持分组数据和单组数据处理
- 自动化的批量分析流程
- 错误容忍和异常处理

## 语义角色定义

### 核心论元角色
```python
SEMANTIC_ROLES = [
    "ARG0",      # 施事者（动作执行者）
    "ARG1",      # 受事者（动作对象）
    "ARG2",      # 起点、来源、材料等
    "ARG3",      # 终点、目标、受益者等
    "ARG4",      # 其他补语
    "ARGM-CND",  # 条件
    "ARGM-LOC",  # 地点
    "ARGM-MNR",  # 方式
    "ARGM-TMP"   # 时间
]
```

### 语义角色说明
- **ARG0**: 施事者，通常是"系统"
- **ARG1**: 受事者，通常是被处理的对象
- **ARG2**: 起点或来源信息
- **ARG3**: 终点或目标位置
- **ARG4**: 其他补充信息
- **ARGM-***: 修饰性论元，提供额外的上下文信息

## 核心函数设计

### 1. HanLP 分析函数

#### `analyze_semantic_roles(standardized_data, save_results=True)`

**功能**: 使用 HanLP 进行语义角色标注分析

**技术栈**:
- **模型**: `CLOSE_TOK_POS_NER_SRL_DEP_SDP_CON_ELECTRA_SMALL_ZH`
- **管道**: 完整的 NLP 处理管道（分词、词性、命名实体、语义角色等）

**处理流程**:
1. 初始化 HanLP 完整管道
2. 逐步处理每个标准化动作
3. 对每个处理步骤进行 SRL 分析
4. 转换 HanLP 结果为标准化元组格式
5. 保存带有语义角色信息的结果

**HanLP 结果转换**:
```python
def _call_hanlp_for_step_srl(step_text: str) -> List[tuple]:
    # HanLP 返回: [(词, 角色, 开始位置, 结束位置), ...]
    # 转换为: [("谓词", "ARG0", "施事者", "ARG1", "受事者"), ...]
```

### 2. LLM 分析函数

#### `analyze_semantic_roles_llm(standardized_data, api_key, model="deepseek-chat", ...)`

**功能**: 使用大语言模型进行语义角色分析

**核心参数**:
- `api_key`: OpenAI API 密钥
- `model`: 使用的 GPT 模型（默认 deepseek-chat）
- `max_retries`: 最大重试次数（默认 3）
- `delay`: 重试延迟时间（默认 2秒）

**LLM Prompt 设计**:
```python
system_prompt = """你是一个专业的语义角色标注专家...

语义角色定义：
- ARG0: 施事者（通常是动作的执行者）
- ARG1: 受事者（通常是动作的接受者或对象）
- ...

请分析文本中的语义角色，并以JSON数组格式返回：
[
    ["谓词1", "ARG0", "施事者", "ARG1", "受事者", "ARGM-MNR", "方式"],
    ["谓词2", "ARG0", "施事者", "ARG1", "受事者"]
]

要求：
1. 每个元组第一个元素是谓词
2. 后续元素按 角色类型-角色内容 的格式成对出现
3. 不可返回空数组[]"""
```

**错误处理机制**:
- API 速率限制的指数退避重试
- JSON 解析失败的正则表达式修复
- 网络错误的自动重试
- 响应格式验证

### 3. 统一接口函数

#### `run_semantic_role_analysis(clustered_data, use_llm=False, api_key=None, ...)`

**功能**: 根据配置选择分析方法的统一接口

**参数说明**:
- `clustered_data`: 输入的标准化动作数据
- `use_llm`: 是否使用大语言模型（默认 False，使用 HanLP）
- `api_key`: LLM 模式所需的 API 密钥

**数据结构兼容**:
- 自动识别分组数据和单组数据结构
- 支持 `decomposed_function_groups` 格式
- 支持单一 `standardized_actions` 格式

## 数据结构设计

### 输入数据结构
```python
{
    "standardized_actions": {
        "动作名称": {
            "steps_with_source": [
                {
                    "step": "系统实时采集飞行姿态数据",
                    "req_id": "需求_1",
                    "original_index": 0
                },
                ...
            ],
            "req_sources": ["需求_1", "需求_2"],
            "step_count": 2
        }
    }
}
```

### 输出数据结构
```python
{
    "standardized_actions": {
        "动作名称": {
            "steps_with_source": [
                {
                    "step": "系统实时采集飞行姿态数据",
                    "req_id": "需求_1",
                    "original_index": 0,
                    "semantic_tuples": [
                        ("采集", "ARG0", "系统", "ARG1", "飞行姿态数据", "ARGM-MNR", "实时")
                    ]
                }
            ]
        }
    }
}
```

### 语义元组格式
```python
# 标准格式：("谓词", "角色类型1", "角色内容1", "角色类型2", "角色内容2", ...)
semantic_tuple = ("采集", "ARG0", "系统", "ARG1", "数据", "ARGM-MNR", "实时")
```

## 性能优化策略

### 1. HanLP 优化
- **模型缓存**: 一次加载，重复使用
- **管道复用**: 避免重复初始化
- **内存管理**: 及时释放处理结果

### 2. LLM 优化
- **批量处理**: 减少 API 调用次数
- **智能重试**: 根据错误类型选择重试策略
- **速率控制**: 避免触发 API 限制

```python
# 重试策略
if "rate limit" in error_msg:
    wait_time = delay * (2 ** attempt)  # 指数退避
elif "timeout" in error_msg:
    wait_time = delay * (attempt + 1)   # 线性增加
```

### 3. 数据处理优化
- **流式处理**: 避免大数据集的内存溢出
- **增量保存**: 定期保存中间结果
- **错误隔离**: 单个处理失败不影响整体流程

## 错误处理机制

### HanLP 错误处理
```python
try:
    hanlp_pipeline = hanlp.load('CLOSE_TOK_POS_NER_SRL_DEP_SDP_CON_ELECTRA_SMALL_ZH')
    logging.info("HanLP 完整管道加载成功")
except Exception as e:
    logging.error(f"HanLP 完整管道加载失败: {e}")
    return standardized_data
```

### LLM 错误处理
- **网络异常**: 自动重试机制
- **格式错误**: JSON 解析修复
- **API 限制**: 智能等待策略
- **内容过滤**: 空响应处理

### 数据验证
- 输入数据结构检查
- 语义角色格式验证
- 结果完整性验证

## 配置依赖

### 环境配置
```python
# HanLP 配置
os.environ["HANLP_HOME"] = os.path.join(MODEL_DIR, "hanlp")
SRL_MODEL = 'CLOSE_TOK_POS_NER_SRL_DEP_SDP_CON_ELECTRA_SMALL_ZH'

# OpenAI 配置
BASE_URL = 'https://api.deepseek.com'  # 或其他 API 基址
```

### 依赖包
```python
# NLP 处理
hanlp                  # 中文 NLP 工具包
sentence_transformers  # 文本嵌入（可选）

# 机器学习
pandas, numpy         # 数据处理
umap-learn, hdbscan   # 聚类分析（可选）

# API 交互
openai                # OpenAI API 客户端

# 标准库
json, os, re, time, typing
```

## 使用示例

### HanLP 模式
```python
# 使用 HanLP 进行语义角色分析
result = analyze_semantic_roles(
    standardized_data=clustered_data,
    save_results=True
)
```

### LLM 模式
```python
# 使用大语言模型进行语义角色分析
result = analyze_semantic_roles_llm(
    standardized_data=clustered_data,
    api_key=api_key,
    model="deepseek-chat",
    save_results=True
)
```

### 统一接口
```python
# 根据配置选择分析方法
result = run_semantic_role_analysis(
    clustered_data=data,
    use_llm=True,          # 使用 LLM 模式
    api_key=api_key,
    model="deepseek-chat",
    save_results=True
)

# 或者使用 HanLP 模式
result = run_semantic_role_analysis(
    clustered_data=data,
    use_llm=False,         # 使用 HanLP 模式
    save_results=True
)
```

## 输出文件格式

### 文件命名
- **HanLP 结果**: `semantic_roles_hanlp_{timestamp}.json`
- **LLM 结果**: `semantic_roles_analysis_{timestamp}.json`
- **统一接口**: `semantic_roles_{timestamp}.json`

### 文件内容
```json
{
    "standardized_actions": {
        "飞行数据采集": {
            "steps_with_source": [
                {
                    "step": "系统实时采集飞行姿态数据",
                    "req_id": "需求_1",
                    "original_index": 0,
                    "semantic_tuples": [
                        ["采集", "ARG0", "系统", "ARG1", "飞行姿态数据", "ARGM-MNR", "实时"]
                    ]
                }
            ]
        }
    }
}
```

## 质量控制

### 分析质量指标
- 谓词识别准确率
- 论元角色分配正确率
- 语义完整性评估

### 结果验证
- 自动化格式检查
- 语义一致性验证
- 人工抽样审核

## 扩展建议

### 功能扩展
1. **多语言支持**: 扩展对英文等其他语言的支持
2. **领域适配**: 针对特定领域优化语义角色标注
3. **增量学习**: 支持基于标注数据的模型微调
4. **质量评估**: 添加自动化的语义分析质量评估

### 性能改进
1. **并行处理**: 支持多线程/多进程并行分析
2. **缓存机制**: 实现智能缓存避免重复分析
3. **流式处理**: 支持大规模数据的流式分析
4. **GPU 加速**: 利用 GPU 加速 HanLP 模型推理

### 工具集成
1. **可视化界面**: 提供语义角色标注的可视化展示
2. **标注工具**: 开发人工标注和校正工具
3. **API 服务**: 封装为 Web API 服务
4. **数据导出**: 支持多种格式的结果导出
