import json
from commonReq.document_reader import RequirementExtractor
from commonReq.requirement_decomposer import RequirementDecomposer
from commonReq.cluster_similar_reqs import cluster_similar_requirements_llm, cluster_similar_requirements
from commonReq.analyze_semantic_role import run_semantic_role_analysis 
from commonReq.cluster_and_standardize_actions import cluster_and_standardize_actions
from commonReq.generate_comparison_matrix import run_analyze_requirement_variability
import os
from commonReq.config import OUTPUT_DIR, DOCS_DIR, DATA_DIR

if __name__ == "__main__":
    api_key = os.getenv("DEEPSEEK_API_KEY")

    # # 配置OpenAI API密钥
    # extractor = RequirementExtractor(api_key=api_key,model="deepseek-chat")  # 使用 DeepSeek 的模型
    
    # # DOCS_DIR目录下的示例文件
    # example_file = os.path.join(DOCS_DIR, "WEA-TH-1-需求文档-1.5.docx")

    # # 处理文件
    # result_count = extractor.extract_requirements(example_file)
    # print(f"成功保存 {result_count} 个需求文件到 {OUTPUT_DIR} 目录")

    # 聚类相似需求
    # print("开始LLM聚类相似需求...")
    # input_dir = os.path.join(DATA_DIR, "weather_mook")
    # clustered_reqs = cluster_similar_requirements_llm(
    #     re_dirs = input_dir,
    #     api_key=api_key,
    #     model="deepseek-chat",  # 使用 DeepSeek 的模型
    # )
    
    # print("开始Bertopic聚类相似需求...")
    # clustered_reqs = cluster_similar_requirements(input_dir)

    # decomposer = RequirementDecomposer(
    #     api_key=api_key,
    #     model="deepseek-chat"  # 使用 DeepSeek 的模型
    # )
    
    # # 处理需求目录
    # print("开始处理需求...")
    # decomposer.process_requirements_from_cluster(r'output\commonReq\product_line_variants.json')
    # print("需求处理完成！结果已保存。")


    # decomposed_req = r'output\decomposed_requirements\decomposed_product_line_variants.json'
    # if os.path.exists(decomposed_req):
    #     print(f"开始动作聚类分析: {decomposed_req}")
    #     with open(decomposed_req, 'r', encoding='utf-8') as f:
    #         decomposed_req = json.load(f)
    #     res = cluster_and_standardize_actions(decomposed_req, api_key=api_key, model="deepseek-chat")
    
    # print("开始动作语义角色分析...")
    # cluster_and_standardize_actions_req = r'output\commonReq\new_cluster_and_standardized_reqs.json'
    # if os.path.exists(cluster_and_standardize_actions_req):
    #     print(f"开始语义角色分析: {cluster_and_standardize_actions_req}")
    #     with open(cluster_and_standardize_actions_req, 'r', encoding='utf-8') as f:
    #         cluster_and_standardize_actions_req = json.load(f)
    #     run_semantic_role_analysis(cluster_and_standardize_actions_req,use_llm=True,api_key=api_key, model="deepseek-chat")

    print("开始配置项分析...")
    semantic_roles_actions_req = r'output\commonReq\new_semantic_roles.json'
    if os.path.exists(semantic_roles_actions_req):
        print(f"开始配置项分析: {semantic_roles_actions_req}")
        with open(semantic_roles_actions_req, 'r', encoding='utf-8') as f:
            semantic_roles_actions_req = json.load(f)
        run_analyze_requirement_variability(semantic_roles_actions_req,api_key=api_key, model="deepseek-chat")