# Document Reader 模块设计文档

## 概述

`document_reader.py` 模块提供了一个智能的需求文档解析器，能够从 PDF 和 DOCX 格式的需求文档中自动提取功能性需求。该模块使用大语言模型（LLM）进行智能解析，并通过滑动窗口技术处理大型文档。

## 核心类：RequirementExtractor

### 功能特性

1. **多格式文档支持**
   - 支持 PDF 文档解析
   - 支持 DOCX 文档解析
   - 保留基础文档格式和结构

2. **智能需求提取**
   - 基于 LLM 的需求识别
   - 自动过滤非功能性内容
   - 结构化需求输出

3. **大文档处理**
   - 滑动窗口技术处理长文档
   - 可配置窗口大小和重叠度
   - 防止内容丢失

4. **结果去重与存储**
   - 基于内容哈希的智能去重
   - JSONL 格式存储
   - 自动生成安全文件名

## 架构设计

### 类初始化参数

```python
def __init__(self, api_key, model="deepseek-chat", window_size=1000, overlap=200):
```

- `api_key`: OpenAI API 密钥
- `model`: 使用的 LLM 模型（默认 deepseek-chat）
- `window_size`: 滑动窗口大小（字符数）
- `overlap`: 窗口重叠大小（字符数）

### 核心方法

#### 1. 文档加载方法

- `load_file(file_path)`: 主文档加载接口
- `_read_pdf(file_path)`: PDF 文档解析
- `_read_docx(file_path)`: DOCX 文档解析

**特点:**
- 保留段落结构
- 处理换行符（替换为 ↵ 符号）
- 错误处理和文件存在性检查

#### 2. 文本窗口处理

- `_create_sliding_windows(text)`: 创建重叠文本窗口

**算法特点:**
- 防止无限循环的安全机制
- 确保窗口位置前进
- 动态调整重叠大小

#### 3. LLM 交互

- `_call_llm(text_chunk)`: 调用大语言模型进行需求提取

**Prompt 设计:**
- System Prompt：定义助手角色和任务
- User Prompt：提供具体文本和输出格式要求
- 温度设置：0.1（确保稳定输出）

#### 4. 响应处理

- `_parse_llm_response(response)`: 解析和验证 LLM 响应

**验证机制:**
- JSON 格式验证
- 数据类型检查
- 必需字段验证
- 内容清理

#### 5. 结果存储

- `_generate_jsonl_filename(input_file_path)`: 生成安全文件名
- `load_requirements_from_jsonl(jsonl_path)`: 从存储文件加载需求

## 数据结构

### 需求对象格式

```json
{
  "req_id": "需求 ID（可选）",
  "req_name": "需求简短描述（可选）",
  "req_content": "需求的原始文本内容（必需）"
}
```

### 存储格式

- **文件格式**: JSONL（JSON Lines）
- **编码**: UTF-8
- **位置**: `{OUTPUT_DIR}/requirements/`
- **命名规则**: `{文档名}_requirements.jsonl`

## 工作流程

### 主处理流程 (`extract_requirements`)

1. **文档加载**
   - 根据文件扩展名选择解析器
   - 提取并格式化文本内容

2. **文本分割**
   - 创建重叠的滑动窗口
   - 确保内容完整性

3. **并行处理**
   - 逐个处理每个文本窗口
   - 调用 LLM 进行需求识别

4. **结果合并**
   - 收集所有窗口的处理结果
   - 进行内容去重

5. **数据存储**
   - 生成 JSONL 文件
   - 返回处理统计信息

## 错误处理

### 文件处理错误
- 文件不存在异常
- 不支持的文件格式异常
- 文件读取权限问题

### LLM 交互错误
- API 调用失败处理
- 响应格式错误处理
- JSON 解析异常处理

### 数据验证错误
- 空内容过滤
- 数据类型验证
- 必需字段检查

## 性能优化

### 内存管理
- 滑动窗口避免全文加载
- 及时释放不需要的数据
- 分批处理大型文档

### 网络优化
- LLM 调用错误重试机制
- 合理的温度和 token 限制
- 批量处理减少 API 调用

### 存储优化
- 基于内容哈希的去重
- JSONL 格式便于增量处理
- 安全的文件名生成

## 配置依赖

### 外部配置
- `BASE_URL`: OpenAI API 基础 URL
- `OUTPUT_DIR`: 输出目录路径
- `DOCS_DIR`: 文档目录路径

### Python 依赖
- `pdfplumber`: PDF 解析
- `python-docx`: DOCX 解析  
- `openai`: OpenAI API 客户端
- 标准库：`os`, `re`, `json`, `hashlib`

## 使用示例

```python
# 初始化提取器
api_key = os.getenv("DEEPSEEK_API_KEY")
extractor = RequirementExtractor(api_key=api_key)

# 处理文档
file_path = "path/to/requirement_document.docx"
result_count = extractor.extract_requirements(file_path)
print(f"成功提取 {result_count} 个需求")

# 加载已处理的需求
jsonl_path = "output/requirements/document_requirements.jsonl"
requirements = extractor.load_requirements_from_jsonl(jsonl_path)
```

## 扩展建议

### 功能扩展
1. 支持更多文档格式（TXT、RTF 等）
2. 增加需求分类和优先级标注
3. 支持多语言需求文档
4. 添加需求质量评估

### 性能改进
1. 实现异步处理提升速度
2. 增加本地缓存减少重复处理
3. 支持分布式处理大批量文档
4. 优化提示词提高提取准确率

### 用户体验
1. 添加进度条显示处理状态
2. 提供可视化的需求预览
3. 支持交互式需求验证和编辑
4. 增加批量处理模式
