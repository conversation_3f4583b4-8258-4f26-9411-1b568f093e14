# Config 模块设计文档

## 概述

`config.py` 模块是整个 `commonReq` 项目的配置中心，负责定义项目的全局配置参数、路径设置、日志配置和环境变量管理。该模块采用集中化配置管理的设计理念，确保项目各模块间的配置一致性和可维护性。

## 功能特性

### 1. 路径配置管理
- 动态项目根目录识别
- 标准化的目录结构定义
- 跨平台路径兼容性

### 2. 日志系统配置
- 统一的日志格式和级别设置
- 文件日志持久化存储
- UTF-8 编码支持中文日志

### 3. 模型和服务配置
- AI 模型路径和服务配置
- 外部 API 服务地址管理
- 模型缓存目录设置

### 4. 环境变量管理
- 第三方库环境变量设置
- 模型和数据路径配置
- 服务依赖配置

## 配置结构设计

### 1. 路径配置

#### 项目根目录动态识别
```python
# 动态获取项目根目录路径
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
```

**设计原理**:
- `__file__`: 当前配置文件的绝对路径
- `os.path.dirname()`: 获取父目录，两次调用返回项目根目录
- `os.path.abspath()`: 确保获得绝对路径，避免相对路径问题

**优势**:
- 自动适配不同的部署环境
- 无需手工配置项目路径
- 支持项目目录的重命名和移动

#### 标准化目录结构
```python
# 定义数据目录路径
DATA_DIR = os.path.join(PROJECT_ROOT, 'data')      # 数据存储目录
MODEL_DIR = os.path.join(PROJECT_ROOT, 'models')   # 模型文件目录
OUTPUT_DIR = os.path.join(PROJECT_ROOT, 'output')  # 输出结果目录
LOG_DIR = os.path.join(PROJECT_ROOT, 'logs')       # 日志文件目录  
DOCS_DIR = os.path.join(PROJECT_ROOT, 'docs')      # 文档资源目录
```

**目录功能**:
- **`data/`**: 存储输入数据、需求文档、训练数据等
- **`models/`**: 存储预训练模型、模型缓存、模型配置等
- **`output/`**: 存储处理结果、分析报告、中间输出等
- **`logs/`**: 存储运行日志、错误日志、调试信息等
- **`docs/`**: 存储文档资源、配置文件、说明文档等

### 2. 服务配置

#### API 服务配置
```python
BASE_URL = 'https://api.deepseek.com'
```

**用途**:
- OpenAI 兼容 API 的基础地址
- 支持 DeepSeek、GPT 等多种 LLM 服务
- 便于服务提供商的切换

#### 模型路径配置
```python
# 嵌入模型路径
EMBEDDING_MODEL = os.path.join(MODEL_DIR, "sentence_transformers", "BAAI_bge-base-zh-v1.5")

# 数据集路径
COMMONREQ_DATA = os.path.join(DATA_DIR, "commonreq_only_abs")

# 语义角色标注模型
SRL_MODEL = 'CLOSE_TOK_POS_NER_SRL_DEP_SDP_CON_ELECTRA_SMALL_ZH'
```

**模型说明**:
- **嵌入模型**: 中文文本嵌入，支持语义相似性计算
- **数据集**: 需求抽象数据集，用于聚类和分析
- **SRL 模型**: HanLP 语义角色标注模型标识符

### 3. 日志系统配置

#### 日志配置设计
```python
# 创建日志文件
log_file_path = os.path.join("logs", "topic_clustering.log")
file_handler = logging.FileHandler(log_file_path, encoding='utf-8')
logging.basicConfig(
    handlers=[file_handler],
    level=logging.INFO,                    # 日志级别
    format="%(asctime)s - %(message)s",    # 日志格式
    datefmt="%Y-%m-%d %H:%M:%S",          # 时间格式
)
```

**日志特性**:
- **UTF-8 编码**: 支持中文日志输出
- **INFO 级别**: 记录重要的处理信息
- **时间戳**: 精确到秒的时间记录
- **简洁格式**: 时间 + 消息的简洁格式

**日志级别说明**:
- `DEBUG`: 详细的调试信息
- `INFO`: 一般信息，如处理进度、状态变化
- `WARNING`: 警告信息，如配置问题、性能提醒
- `ERROR`: 错误信息，如处理失败、异常情况
- `CRITICAL`: 严重错误，如系统崩溃

### 4. 环境变量配置

#### HanLP 环境配置
```python
# 设置 HanLP 的环境变量 cache 目录
os.environ["HANLP_HOME"] = os.path.join(MODEL_DIR, "hanlp")
```

**用途**:
- 指定 HanLP 模型的缓存目录
- 避免模型下载到系统默认位置
- 便于项目的整体迁移和管理

**重要说明**: 此配置必须在导入 HanLP 之前设置，否则会使用默认路径。

## 配置使用模式

### 1. 模块导入方式
```python
# 完整导入
from commonReq.config import (
    BASE_URL, OUTPUT_DIR, DOCS_DIR, logging,
    EMBEDDING_MODEL, SRL_MODEL, COMMONREQ_DATA
)

# 按需导入
from commonReq.config import OUTPUT_DIR, logging

# 全量导入（不推荐）
from commonReq.config import *
```

### 2. 路径构建模式
```python
# 正确的路径构建方式
output_file = os.path.join(OUTPUT_DIR, "results", "analysis.json")
model_path = os.path.join(MODEL_DIR, "custom_model")

# 避免硬编码路径
# 错误示例：output_file = "output/results/analysis.json"
```

### 3. 日志使用模式
```python
# 导入日志对象
from commonReq.config import logging

# 使用日志记录
logging.info("开始处理需求聚类")
logging.warning("API调用速率接近限制")
logging.error(f"处理失败: {error_message}")
```

## 配置扩展设计

### 1. 环境区分配置
```python
# 可选的环境配置扩展
import os

# 获取环境变量，默认为开发环境
ENVIRONMENT = os.getenv('COMMONREQ_ENV', 'development')

if ENVIRONMENT == 'production':
    # 生产环境配置
    LOG_LEVEL = logging.WARNING
    API_TIMEOUT = 60
elif ENVIRONMENT == 'testing':
    # 测试环境配置  
    LOG_LEVEL = logging.DEBUG
    API_TIMEOUT = 30
else:
    # 开发环境配置
    LOG_LEVEL = logging.INFO
    API_TIMEOUT = 30
```

### 2. 配置验证机制
```python
def validate_config():
    """验证配置的完整性和正确性"""
    required_dirs = [DATA_DIR, MODEL_DIR, OUTPUT_DIR, LOG_DIR, DOCS_DIR]
    
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)
            logging.info(f"创建缺失目录: {dir_path}")
    
    # 验证关键文件和模型
    if not os.path.exists(EMBEDDING_MODEL):
        logging.warning(f"嵌入模型不存在: {EMBEDDING_MODEL}")
    
    if not os.path.exists(COMMONREQ_DATA):
        logging.warning(f"数据集不存在: {COMMONREQ_DATA}")

# 可选的自动验证
# validate_config()
```

### 3. 动态配置更新
```python
class ConfigManager:
    """配置管理器，支持动态配置更新"""
    
    def __init__(self):
        self.config = {
            'base_url': BASE_URL,
            'embedding_model': EMBEDDING_MODEL,
            'log_level': logging.INFO
        }
    
    def update_config(self, key, value):
        """动态更新配置项"""
        if key in self.config:
            self.config[key] = value
            logging.info(f"配置更新: {key} = {value}")
        else:
            logging.warning(f"未知配置项: {key}")
    
    def get_config(self, key, default=None):
        """获取配置项"""
        return self.config.get(key, default)

# 使用示例
# config_manager = ConfigManager()
# config_manager.update_config('base_url', 'https://api.openai.com')
```

## 最佳实践建议

### 1. 配置项命名规范
```python
# 推荐的命名方式
API_BASE_URL = "https://api.example.com"    # 全大写，下划线分隔
DEFAULT_MODEL = "gpt-4"                     # 常量使用全大写
max_retries = 3                             # 变量使用小写

# 路径相关统一使用 _DIR 后缀
DATA_DIR = "data"
OUTPUT_DIR = "output"
```

### 2. 配置文档化
```python
# 推荐为重要配置添加注释
BASE_URL = 'https://api.deepseek.com'  # DeepSeek API 服务地址
EMBEDDING_MODEL = os.path.join(        # 中文文本嵌入模型路径
    MODEL_DIR, "sentence_transformers", "BAAI_bge-base-zh-v1.5"
)
```

### 3. 配置安全性
```python
# 敏感信息使用环境变量
API_KEY = os.getenv('DEEPSEEK_API_KEY')  # 从环境变量读取
if not API_KEY:
    logging.warning("未设置 API 密钥环境变量")

# 避免在代码中硬编码敏感信息
# 错误示例：API_KEY = "sk-xxx..."
```

## 依赖关系管理

### 内部依赖
```python
# config.py 作为基础配置，被其他模块依赖
# document_reader.py → config.py
# cluster_similar_reqs.py → config.py  
# requirement_decomposer.py → config.py
# 等等...
```

### 外部依赖
```python
# 标准库依赖
import os      # 路径和环境变量操作
import logging # 日志系统

# 第三方库依赖（间接）
# hanlp        # 通过环境变量 HANLP_HOME 影响
# openai       # 通过 BASE_URL 配置影响
```

## 配置测试和验证

### 配置完整性测试
```python
def test_config_completeness():
    """测试配置的完整性"""
    # 测试必需的路径配置
    assert PROJECT_ROOT is not None
    assert os.path.isdir(PROJECT_ROOT)
    
    # 测试目录配置
    required_vars = ['DATA_DIR', 'MODEL_DIR', 'OUTPUT_DIR', 'LOG_DIR', 'DOCS_DIR']
    for var_name in required_vars:
        assert var_name in globals()
        assert globals()[var_name] is not None
    
    # 测试服务配置
    assert BASE_URL is not None
    assert BASE_URL.startswith('http')
    
    print("✓ 所有配置项验证通过")

# 运行测试
# test_config_completeness()
```

### 环境配置测试
```python
def test_environment_setup():
    """测试环境配置是否正确"""
    # 测试 HanLP 环境变量
    assert 'HANLP_HOME' in os.environ
    assert os.path.exists(os.environ['HANLP_HOME'])
    
    # 测试日志配置
    assert logging.getLogger().level == logging.INFO
    
    # 测试模型路径
    embedding_dir = os.path.dirname(EMBEDDING_MODEL)
    assert os.path.exists(embedding_dir)
    
    print("✓ 环境配置验证通过")

# 运行测试  
# test_environment_setup()
```

## 常见问题和解决方案

### 1. 路径问题
**问题**: 在不同操作系统或部署环境中路径不正确

**解决方案**:
```python
# 始终使用 os.path.join() 构建路径
correct_path = os.path.join(PROJECT_ROOT, 'data', 'file.json')

# 避免硬编码路径分隔符
# wrong_path = PROJECT_ROOT + '/data/file.json'  # 错误方式
```

### 2. 编码问题
**问题**: 中文日志或文件名显示异常

**解决方案**:
```python
# 确保日志文件使用 UTF-8 编码
file_handler = logging.FileHandler(log_file_path, encoding='utf-8')

# 文件操作时显式指定编码
with open(file_path, 'w', encoding='utf-8') as f:
    json.dump(data, f, ensure_ascii=False, indent=2)
```

### 3. 环境变量问题
**问题**: HanLP 或其他库的环境变量未生效

**解决方案**:
```python
# 确保在导入相关库之前设置环境变量
os.environ["HANLP_HOME"] = os.path.join(MODEL_DIR, "hanlp")

# 然后再导入使用该环境变量的库
import hanlp  # 此时会使用上面设置的路径
```

## 升级和维护指南

### 版本兼容性
- 保持向后兼容的配置接口
- 新增配置项时提供默认值
- 废弃配置项时提供迁移说明

### 配置变更流程
1. **评估影响**: 分析配置变更对其他模块的影响
2. **向后兼容**: 确保新配置与旧配置兼容
3. **文档更新**: 更新配置说明和使用示例
4. **测试验证**: 运行完整的配置测试套件

### 监控和维护
- 定期检查配置文件的完整性
- 监控日志输出确保配置生效
- 及时清理过期的配置项和注释

通过 `config.py` 的集中化配置管理，整个项目的配置保持一致性和可维护性，为其他模块提供了稳定可靠的配置基础。
