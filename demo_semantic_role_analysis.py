#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语义角色分析演示脚本

演示如何使用修改后的语义角色分析函数：
1. analyze_semantic_roles - 使用 HanLP 模型
2. analyze_semantic_roles_llm - 使用大语言模型
对需求分解数据中的处理步骤进行语义角色分析
"""

import json
import os
from commonReq.analyze_semantic_role import analyze_semantic_roles, analyze_semantic_roles_llm
from commonReq.config import logging

def demo_hanlp_semantic_role_analysis():
    """演示使用 HanLP 进行语义角色分析"""
    
    # 示例需求数据（基于你提供的格式）
    requirements_data = {
        "core_function": "无人机飞行姿态、速度、位置数据的实时采集，飞行调整指令的计算与执行，多种飞行模式支持，以及稳定飞行和抗干扰能力",
        "original_requirements": [
            {
                "req_content": "无人机系统的飞行控制模块必须能够实时采集飞行姿态、速度和位置等关键数据，通过精准的算法计算出飞行调整指令，并可靠地驱动执行机构。",
                "req_id": "需求_1"
            }
        ],
        "decomposed_requirements": [
            {
                "req_id": "需求_1",
                "req_name": "功能组1-需求1",
                "req_content": "无人机系统的飞行控制模块必须能够实时采集飞行姿态、速度和位置等关键数据，通过精准的算法计算出飞行调整指令，并可靠地驱动执行机构。",
                "input": [
                    "飞行姿态数据",
                    "速度数据",
                    "位置数据"
                ],
                "output": [
                    "飞行调整指令",
                    "执行机构驱动信号"
                ],
                "processing_steps": [
                    "系统实时采集飞行姿态、速度和位置数据",
                    "系统通过精准算法计算飞行调整指令",
                    "系统可靠驱动执行机构"
                ],
                "group_id": "1",
                "core_function": "无人机飞行姿态、速度、位置数据的实时采集，飞行调整指令的计算与执行",
                "original_source": ""
            }
        ],
        "group_stats": {
            "total_requirements": 1,
            "successful_decompositions": 1,
            "failed_decompositions": 0
        }
    }
    
    try:
        logging.info("开始HanLP语义角色分析演示...")
        
        # 调用 HanLP 语义角色分析函数
        result_data = analyze_semantic_roles(
            requirements_data=requirements_data,
            save_results=True
        )
        
        # 展示结果
        logging.info("\n=== HanLP分析结果展示 ===")
        for requirement in result_data["decomposed_requirements"]:
            req_id = requirement.get("req_id")
            req_name = requirement.get("req_name")
            semantic_roles = requirement.get("semantic_roles", [])
            
            logging.info(f"\n需求: {req_id} - {req_name}")
            logging.info(f"处理步骤数量: {len(requirement.get('processing_steps', []))}")
            logging.info(f"语义角色分析结果:")
            
            for step_idx, step_tuples in enumerate(semantic_roles):
                step_text = requirement['processing_steps'][step_idx] if step_idx < len(requirement.get('processing_steps', [])) else f"步骤{step_idx+1}"
                logging.info(f"  步骤 {step_idx + 1}: {step_text[:50]}...")
                
                if step_tuples:
                    for tuple_idx, srl_tuple in enumerate(step_tuples):
                        logging.info(f"    元组 {tuple_idx + 1}: {srl_tuple}")
                else:
                    logging.info("    (未发现语义角色)")
                    
        return result_data
        
    except Exception as e:
        logging.error(f"HanLP语义角色分析演示失败: {e}")
        return requirements_data


def demo_semantic_role_analysis():
    """演示使用大语言模型进行语义角色分析"""
    
    # 示例需求数据（基于你提供的格式）
    requirements_data = {
        "core_function": "无人机飞行姿态、速度、位置数据的实时采集，飞行调整指令的计算与执行，多种飞行模式支持，以及稳定飞行和抗干扰能力",
        "original_requirements": [
            {
                "req_content": "无人机系统的飞行控制模块必须能够实时采集飞行姿态、速度和位置等关键数据，通过精准的算法计算出飞行调整指令，并可靠地驱动执行机构，使无人机在各种复杂环境中保持稳定飞行，具备应对外界干扰的能力。模块需支持多种飞行模式，如手动控制、自动驾驶以及任务规划导航模式，并确保在极端条件下仍具备高可靠性和容错性。",
                "req_id": "需求_1"
            },
            {
                "req_content": "无人机系统的飞行控制模块需要在飞行过程中持续采集并监测姿态、速度以及高度等关键数据，通过高精度算法生成最优调整指令，并驱动执行机构以在各种复杂环境下保持平稳飞行。该模块需具备快速响应外界干扰的能力，支持手动控制、半自动以及预设任务自主飞行等多种模式，并在极端气象或突发情况下提供高可靠性和故障安全机制。",
                "req_id": "需求_11"
            }
        ],
        "decomposed_requirements": [
            {
                "req_id": "需求_1",
                "req_name": "功能组1-需求1",
                "req_content": "无人机系统的飞行控制模块必须能够实时采集飞行姿态、速度和位置等关键数据，通过精准的算法计算出飞行调整指令，并可靠地驱动执行机构，使无人机在各种复杂环境中保持稳定飞行，具备应对外界干扰的能力。模块需支持多种飞行模式，如手动控制、自动驾驶以及任务规划导航模式，并确保在极端条件下仍具备高可靠性和容错性。",
                "input": [
                    "飞行姿态数据",
                    "速度数据",
                    "位置数据",
                    "外界干扰信号",
                    "飞行模式选择指令"
                ],
                "output": [
                    "飞行调整指令",
                    "执行机构驱动信号",
                    "飞行状态反馈"
                ],
                "processing_steps": [
                    "系统实时采集飞行姿态、速度和位置数据",
                    "系统通过精准算法计算飞行调整指令",
                    "系统可靠驱动执行机构",
                    "系统应对外界干扰以保持稳定飞行",
                    "系统支持手动控制模式"
                ],
                "group_id": "1",
                "core_function": "无人机飞行姿态、速度、位置数据的实时采集，飞行调整指令的计算与执行，多种飞行模式支持，以及稳定飞行和抗干扰能力",
                "original_source": ""
            },
            {
                "req_id": "需求_11",
                "req_name": "功能组1-需求2",
                "req_content": "无人机系统的飞行控制模块需要在飞行过程中持续采集并监测姿态、速度以及高度等关键数据，通过高精度算法生成最优调整指令，并驱动执行机构以在各种复杂环境下保持平稳飞行。该模块需具备快速响应外界干扰的能力，支持手动控制、半自动以及预设任务自主飞行等多种模式，并在极端气象或突发情况下提供高可靠性和故障安全机制。",
                "input": [
                    "姿态数据",
                    "速度数据",
                    "高度数据",
                    "外界干扰信号"
                ],
                "output": [
                    "最优调整指令",
                    "执行机构驱动信号",
                    "故障安全响应"
                ],
                "processing_steps": [
                    "系统持续采集姿态、速度、高度数据",
                    "系统监测关键数据",
                    "系统通过高精度算法生成最优调整指令",
                    "系统驱动执行机构保持平稳飞行"
                ],
                "group_id": "1",
                "core_function": "无人机飞行姿态、速度、位置数据的实时采集，飞行调整指令的计算与执行，多种飞行模式支持，以及稳定飞行和抗干扰能力",
                "original_source": ""
            }
        ],
        "group_stats": {
            "total_requirements": 2,
            "successful_decompositions": 2,
            "failed_decompositions": 0
        }
    }

    # 设置API密钥（实际使用时请设置你的OpenAI API密钥）
    api_key = os.getenv("DEEPSEEK_API_KEY" )
    
    if api_key == "your_openai_api_key_here":
        logging.warning("请设置有效的OpenAI API密钥")
        logging.info("演示语义角色分析的输入输出格式...")
        
        # 展示输入格式
        print("=== 输入数据格式 ===")
        print("requirements_data 应包含以下结构:")
        print("- decomposed_requirements: 分解需求列表")
        print("  - 每个需求包含 processing_steps: 处理步骤列表")
        print("\n示例输入:")
        sample_requirement = requirements_data["decomposed_requirements"][0]
        print(f"需求ID: {sample_requirement['req_id']}")
        print(f"处理步骤数量: {len(sample_requirement['processing_steps'])}")
        for i, step in enumerate(sample_requirement['processing_steps'][:3]):
            print(f"  步骤{i+1}: {step}")
        if len(sample_requirement['processing_steps']) > 3:
            print(f"  ... (共{len(sample_requirement['processing_steps'])}个步骤)")
            
        print("\n=== 输出数据格式 ===")
        print("函数将在每个需求中添加 'semantic_roles' 字段:")
        print("- semantic_roles: 语义角色分析结果列表")
        print("  - 每个元素对应一个处理步骤的语义角色元组列表")
        print("  - 每个元组格式: (谓词, 角色类型1, 角色内容1, 角色类型2, 角色内容2, ...)")
        
        print("\n示例输出格式:")
        example_output = {
            "semantic_roles": [
                [
                    ("采集", "ARG0", "系统", "ARG1", "飞行姿态数据", "ARGM-MNR", "实时"),
                    ("采集", "ARG0", "系统", "ARG1", "速度数据"),
                    ("采集", "ARG0", "系统", "ARG1", "位置数据")
                ],
                [
                    ("计算", "ARG0", "系统", "ARG1", "飞行调整指令", "ARGM-MNR", "通过精准算法")
                ]
            ]
        }
        print(json.dumps(example_output, ensure_ascii=False, indent=2))
        
        return requirements_data
    
    try:
        logging.info("开始语义角色分析演示...")
        
        # 调用语义角色分析函数
        result_data = analyze_semantic_roles_llm(
            requirements_data=requirements_data,
            api_key=api_key,
            model="deepseek-chat",
            save_results=True,
            max_retries=3,
            delay=2
        )
        
        # 展示结果
        logging.info("\n=== LLM分析结果展示 ===")
        for requirement in result_data["decomposed_requirements"]:
            req_id = requirement.get("req_id")
            req_name = requirement.get("req_name")
            semantic_roles = requirement.get("semantic_roles", [])
            
            logging.info(f"\n需求: {req_id} - {req_name}")
            logging.info(f"处理步骤数量: {len(requirement.get('processing_steps', []))}")
            logging.info(f"语义角色分析结果:")
            
            for step_idx, step_tuples in enumerate(semantic_roles):
                step_text = requirement['processing_steps'][step_idx] if step_idx < len(requirement.get('processing_steps', [])) else f"步骤{step_idx+1}"
                logging.info(f"  步骤 {step_idx + 1}: {step_text[:50]}...")
                
                if step_tuples:
                    for tuple_idx, srl_tuple in enumerate(step_tuples):
                        logging.info(f"    元组 {tuple_idx + 1}: {srl_tuple}")
                else:
                    logging.info("    (未发现语义角色)")
                    
        return result_data
        
    except Exception as e:
        logging.error(f"语义角色分析演示失败: {e}")
        return requirements_data

if __name__ == "__main__":
    print("=== 语义角色分析演示程序 ===")
    print("1. HanLP 语义角色分析")
    print("2. LLM 语义角色分析")
    
    choice = input("请选择演示类型 (1/2) [默认: 1]: ").strip()
    
    if choice == "2":
        print("\n运行 LLM 语义角色分析演示...")
        result = demo_semantic_role_analysis()
        output_file = "demo_llm_semantic_role_output.json"
    else:
        print("\n运行 HanLP 语义角色分析演示...")
        result = demo_hanlp_semantic_role_analysis()
        output_file = "demo_hanlp_semantic_role_output.json"
    
    # 保存演示结果
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"\n演示结果已保存到: {output_file}")
    except Exception as e:
        print(f"保存演示结果失败: {e}")
