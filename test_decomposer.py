#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 RequirementDecomposer 的 process_requirements_from_cluster 函数
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from commonReq.requirement_decomposer import RequirementDecomposer
from commonReq.config import OUTPUT_DIR

def test_process_requirements_from_cluster():
    """测试处理聚类结果文件的功能"""
    
    # 聚类结果文件路径
    cluster_file = r"e:\Codebase\common-requirements\output\commonReq\llm_same_function_requirements.json"
    
    if not os.path.exists(cluster_file):
        print(f"聚类文件不存在: {cluster_file}")
        return False
    
    print(f"找到聚类文件: {cluster_file}")
    
    # 初始化分解器（使用测试密钥）
    try:
        api_key = os.getenv("DEEPSEEK_API_KEY")
        decomposer = RequirementDecomposer(
            api_key=api_key,  # 这里需要真实的API密钥
            model="deepseek-chat",
            max_retries=2,
            delay=1
        )
        print("分解器初始化成功")
    except Exception as e:
        print(f"分解器初始化失败: {e}")
        return False
    
    # 先读取并验证聚类文件格式
    try:
        with open(cluster_file, 'r', encoding='utf-8') as f:
            cluster_data = json.load(f)
        
        print("聚类文件格式验证:")
        print(f"  元数据存在: {'metadata' in cluster_data}")
        print(f"  相同功能组: {len(cluster_data.get('same_function_groups', {}))}")
        print(f"  所有功能组: {len(cluster_data.get('all_function_groups', {}))}")
        
        # 统计总需求数
        same_groups = cluster_data.get('same_function_groups', {})
        total_reqs = sum(len(group.get('requirements', [])) for group in same_groups.values())
        print(f"  总需求数: {total_reqs}")
        
        # 显示前几个功能组的信息
        print("\n功能组预览:")
        for i, (group_id, group_info) in enumerate(list(same_groups.items())[:3]):
            core_func = group_info.get('core_function', 'N/A')[:80] + "..." if len(group_info.get('core_function', '')) > 80 else group_info.get('core_function', 'N/A')
            req_count = len(group_info.get('requirements', []))
            print(f"  组 {group_id}: {req_count} 个需求")
            print(f"    核心功能: {core_func}")
            
            # 显示第一个需求的预览
            if group_info.get('requirements'):
                first_req = group_info['requirements'][0]
                req_preview = first_req.get('req_content', first_req.get('text', ''))[:100] + "..."
                print(f"    需求示例: {req_preview}")
        
    except Exception as e:
        print(f"读取聚类文件失败: {e}")
        return False
    
    print(f"\n聚类文件验证成功，包含 {total_reqs} 个需求待分解")
    print("注意: 实际分解需要有效的API密钥")
    
    # 如果有有效的API密钥，可以取消以下注释进行实际测试
    try:
        print("开始处理聚类需求...")
        result = decomposer.process_requirements_from_cluster(cluster_file)
        
        if result:
            print("✓ 聚类需求分解完成!")
            summary = result.get("processing_summary", {})
            print(f"  处理统计: {summary}")
            return True
        else:
            print("✗ 聚类需求分解失败")
            return False
            
    except Exception as e:
        print(f"处理过程中出错: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("=== 测试 process_requirements_from_cluster 函数 ===")
    success = test_process_requirements_from_cluster()
    print(f"\n测试结果: {'通过' if success else '失败'}")
    sys.exit(0 if success else 1)
