# __main__.py 模块设计文档

## 概述

`__main__.py` 模块是整个 `commonReq` 项目的主入口点和工作流程编排器。该模块定义了完整的需求分析流水线，从需求文档提取到最终的可变性配置项生成，提供了一个端到端的自动化需求分析解决方案。

## 功能特性

### 1. 完整的分析流水线
- 需求文档解析与提取
- 相似需求聚类分析
- 需求分解与结构化
- 动作聚类与标准化
- 语义角色标注分析
- 可变性配置项生成

### 2. 模块化工作流设计
- 各阶段独立且可配置
- 支持单独运行特定阶段
- 灵活的数据传递机制

### 3. 渐进式处理能力
- 阶段间结果验证
- 增量处理支持
- 错误恢复机制

## 工作流程设计

### 完整的需求分析流水线

```
文档输入 → 需求提取 → 需求聚类 → 需求分解 → 动作聚类 → 语义分析 → 可变性分析
    ↓         ↓         ↓         ↓         ↓         ↓         ↓
  PDF/DOCX   JSONL    聚类组    结构化    标准动作   语义角色   配置项
```

### 1. 第一阶段：需求文档提取

#### 代码实现
```python
# 配置OpenAI API密钥
extractor = RequirementExtractor(api_key=api_key, model="deepseek-chat")

# DOCS_DIR目录下的示例文件
example_file = os.path.join(DOCS_DIR, "WEA-TH-1-需求文档-1.5.docx")

# 处理文件
result_count = extractor.extract_requirements(example_file)
print(f"成功保存 {result_count} 个需求文件到 {OUTPUT_DIR} 目录")
```

**功能说明**:
- 从 PDF/DOCX 文档中提取需求
- 使用 LLM 智能识别功能性需求
- 输出标准化的 JSONL 格式文件

**输出文件**: `{OUTPUT_DIR}/requirements/{文档名}_requirements.jsonl`

### 2. 第二阶段：需求聚类分析

#### LLM 聚类方式
```python
print("开始LLM聚类相似需求...")
input_dir = os.path.join(DATA_DIR, "weather_mook")
clustered_reqs = cluster_similar_requirements_llm(
    re_dirs=input_dir,
    api_key=api_key,
    model="deepseek-chat"
)
```

#### BERTopic 聚类方式
```python
print("开始Bertopic聚类相似需求...")
clustered_reqs = cluster_similar_requirements(input_dir)
```

**功能说明**:
- 支持两种聚类算法：LLM 智能聚类和 BERTopic 无监督聚类
- 识别相似功能的需求变体
- 生成功能分组和独立需求

**输出文件**: `{OUTPUT_DIR}/commonReq/topic_clustering_results*.json`

### 3. 第三阶段：需求分解

```python
decomposer = RequirementDecomposer(
    api_key=api_key,
    model="deepseek-chat"
)

# 处理需求目录
print("开始处理需求...")
decomposer.process_requirements_from_cluster(r'output\commonReq\product_line_variants.json')
print("需求处理完成！结果已保存。")
```

**功能说明**:
- 将聚类后的需求分解为输入、输出、处理步骤
- 保持功能分组结构
- 为后续分析提供结构化数据

**输出文件**: `{OUTPUT_DIR}/decomposed_requirements/decomposed_*.json`

### 4. 第四阶段：动作聚类与标准化

```python
decomposed_req = r'output\decomposed_requirements\decomposed_product_line_variants.json'
if os.path.exists(decomposed_req):
    print(f"开始动作聚类分析: {decomposed_req}")
    with open(decomposed_req, 'r', encoding='utf-8') as f:
        decomposed_req = json.load(f)
    res = cluster_and_standardize_actions(decomposed_req, api_key=api_key, model="deepseek-chat")
```

**功能说明**:
- 识别相似的处理步骤并进行聚类
- 生成标准化的动作描述
- 维护完整的溯源信息

**输出文件**: `{OUTPUT_DIR}/commonReq/cluster_and_standardized_reqs_*.json`

### 5. 第五阶段：语义角色分析

```python
print("开始动作语义角色分析...")
cluster_and_standardize_actions_req = r'output\commonReq\new_cluster_and_standardized_reqs.json'
if os.path.exists(cluster_and_standardize_actions_req):
    print(f"开始语义角色分析: {cluster_and_standardize_actions_req}")
    with open(cluster_and_standardize_actions_req, 'r', encoding='utf-8') as f:
        cluster_and_standardize_actions_req = json.load(f)
    run_semantic_role_analysis(
        cluster_and_standardize_actions_req,
        use_llm=True, 
        api_key=api_key, 
        model="deepseek-chat"
    )
```

**功能说明**:
- 对标准化动作进行语义角色标注
- 支持 HanLP 和 LLM 两种分析方式
- 生成结构化的语义元组

**输出文件**: `{OUTPUT_DIR}/commonReq/semantic_roles_*.json`

### 6. 第六阶段：可变性分析

```python
print("开始配置项分析...")
semantic_roles_actions_req = r'output\commonReq\new_semantic_roles.json'
if os.path.exists(semantic_roles_actions_req):
    print(f"开始配置项分析: {semantic_roles_actions_req}")
    with open(semantic_roles_actions_req, 'r', encoding='utf-8') as f:
        semantic_roles_actions_req = json.load(f)
    run_analyze_requirement_variability(
        semantic_roles_actions_req,
        api_key=api_key, 
        model="deepseek-chat"
    )
```

**功能说明**:
- 从语义角色中提取配置变化点
- 生成结构化的配置项和候选值
- 为软件产品线配置管理提供基础

**输出文件**: `{OUTPUT_DIR}/comparison_matrices/variability_analysis_*.json`

## 设计模式和架构

### 1. 流水线模式（Pipeline Pattern）
```python
# 每个阶段都有明确的输入和输出
Stage1: Document → Requirements (JSONL)
Stage2: Requirements → Clusters (JSON)  
Stage3: Clusters → Decomposed (JSON)
Stage4: Decomposed → Actions (JSON)
Stage5: Actions → Semantics (JSON)
Stage6: Semantics → Variability (JSON)
```

### 2. 配置驱动模式
```python
# 所有配置都来自环境变量和配置文件
api_key = os.getenv("DEEPSEEK_API_KEY")
from commonReq.config import OUTPUT_DIR, DOCS_DIR, DATA_DIR
```

### 3. 容错和恢复模式
```python
# 每个阶段都检查输入文件是否存在
if os.path.exists(decomposed_req):
    # 执行处理逻辑
    pass
else:
    # 跳过或报错
    print("输入文件不存在，跳过此阶段")
```

## 执行模式设计

### 1. 注释控制的阶段执行
```python
# 当前代码使用注释来控制哪些阶段执行
# 这种设计的优势：
# - 可以快速启用/禁用特定阶段
# - 便于调试和测试单个阶段
# - 避免重复运行已完成的阶段

# 示例：只运行最后阶段
# 前面的阶段都被注释掉
# extractor = RequirementExtractor(...)  # 注释掉
# clustered_reqs = cluster_similar_requirements_llm(...)  # 注释掉

# 只执行配置项分析
print("开始配置项分析...")  # 活跃的代码
```

### 2. 文件存在性检查模式
```python
# 通过检查文件是否存在来决定是否执行
if os.path.exists(semantic_roles_actions_req):
    # 文件存在，执行处理
    run_analyze_requirement_variability(...)
else:
    # 文件不存在，跳过或提示
    print(f"文件不存在: {semantic_roles_actions_req}")
```

## 配置和环境管理

### API 密钥管理
```python
# 从环境变量获取 API 密钥
api_key = os.getenv("DEEPSEEK_API_KEY")

# 使用示例
# Windows: set DEEPSEEK_API_KEY=sk-your-key-here
# Linux/Mac: export DEEPSEEK_API_KEY=sk-your-key-here
```

### 路径配置管理
```python
# 从配置模块导入标准路径
from commonReq.config import OUTPUT_DIR, DOCS_DIR, DATA_DIR

# 构建文件路径
example_file = os.path.join(DOCS_DIR, "WEA-TH-1-需求文档-1.5.docx")
input_dir = os.path.join(DATA_DIR, "weather_mook")
```

## 数据流和文件管理

### 数据流向图
```
docs/WEA-TH-1-需求文档-1.5.docx
    ↓ (RequirementExtractor)
output/requirements/{文档名}_requirements.jsonl
    ↓ (cluster_similar_requirements_llm)
output/commonReq/product_line_variants.json
    ↓ (RequirementDecomposer)
output/decomposed_requirements/decomposed_product_line_variants.json
    ↓ (cluster_and_standardize_actions)
output/commonReq/new_cluster_and_standardized_reqs.json
    ↓ (run_semantic_role_analysis)
output/commonReq/new_semantic_roles.json
    ↓ (run_analyze_requirement_variability)
output/comparison_matrices/variability_analysis_{timestamp}.json
```

### 文件命名约定
- **需求提取**: `{文档名}_requirements.jsonl`
- **聚类结果**: `topic_clustering_results_llm_{timestamp}.json`
- **分解结果**: `decomposed_{原文件名}.json`
- **动作聚类**: `cluster_and_standardized_reqs_{timestamp}.json`
- **语义分析**: `semantic_roles_{timestamp}.json`
- **可变性分析**: `variability_analysis_{timestamp}.json`

## 使用方式和部署

### 1. 命令行执行
```bash
# 作为模块运行
python -m commonReq

# 或者直接执行
cd commonReq
python __main__.py
```

### 2. 环境准备
```bash
# 设置 API 密钥
export DEEPSEEK_API_KEY="sk-your-key-here"

# 确保必要的目录存在
mkdir -p output/requirements
mkdir -p output/commonReq  
mkdir -p output/decomposed_requirements
mkdir -p output/comparison_matrices
mkdir -p logs
```

### 3. 依赖检查
```python
# 确保所有依赖模块可用
required_modules = [
    'commonReq.document_reader',
    'commonReq.requirement_decomposer',
    'commonReq.cluster_similar_reqs',
    'commonReq.analyze_semantic_role',
    'commonReq.cluster_and_standardize_actions',
    'commonReq.generate_comparison_matrix'
]

for module in required_modules:
    try:
        __import__(module)
        print(f"✓ {module}")
    except ImportError as e:
        print(f"✗ {module}: {e}")
```

## 改进建议和扩展

### 1. 命令行参数支持
```python
import argparse

def main():
    parser = argparse.ArgumentParser(description='需求分析流水线')
    parser.add_argument('--stage', choices=[
        'extract', 'cluster', 'decompose', 'actions', 'semantic', 'variability'
    ], help='指定运行的阶段')
    parser.add_argument('--input', help='输入文件路径')
    parser.add_argument('--output', help='输出文件路径')
    parser.add_argument('--model', default='deepseek-chat', help='使用的模型')
    
    args = parser.parse_args()
    
    # 根据参数执行相应阶段
    if args.stage == 'extract':
        run_extraction_stage(args.input, args.model)
    elif args.stage == 'cluster':
        run_clustering_stage(args.input, args.model)
    # ... 其他阶段
```

### 2. 配置文件支持
```python
import yaml

def load_pipeline_config(config_file='pipeline_config.yaml'):
    """加载流水线配置文件"""
    with open(config_file, 'r') as f:
        config = yaml.safe_load(f)
    
    return config

# 示例配置文件 pipeline_config.yaml
"""
pipeline:
  stages:
    - extract
    - cluster
    - decompose
    - actions
    - semantic
    - variability
  
  models:
    default: "deepseek-chat"
    extraction: "gpt-4"
    clustering: "deepseek-chat"
  
  file_paths:
    input_document: "docs/WEA-TH-1-需求文档-1.5.docx"
    cluster_input: "data/weather_mook"
"""
```

### 3. 状态管理和恢复
```python
class PipelineState:
    """流水线状态管理器"""
    
    def __init__(self, state_file='pipeline_state.json'):
        self.state_file = state_file
        self.state = self.load_state()
    
    def load_state(self):
        if os.path.exists(self.state_file):
            with open(self.state_file, 'r') as f:
                return json.load(f)
        return {'completed_stages': [], 'current_stage': None}
    
    def save_state(self):
        with open(self.state_file, 'w') as f:
            json.dump(self.state, f, indent=2)
    
    def mark_stage_complete(self, stage_name, output_file):
        self.state['completed_stages'].append({
            'stage': stage_name,
            'completed_at': time.strftime("%Y-%m-%d %H:%M:%S"),
            'output_file': output_file
        })
        self.save_state()
    
    def is_stage_completed(self, stage_name):
        return any(s['stage'] == stage_name for s in self.state['completed_stages'])
```

### 4. 错误处理和日志增强
```python
import logging
from commonReq.config import logging

def run_pipeline_with_error_handling():
    """带错误处理的流水线执行"""
    try:
        # 阶段1：需求提取
        logging.info("开始第1阶段：需求文档提取")
        extract_stage()
        logging.info("第1阶段完成")
        
        # 阶段2：需求聚类
        logging.info("开始第2阶段：需求聚类")
        cluster_stage()
        logging.info("第2阶段完成")
        
        # ... 其他阶段
        
    except Exception as e:
        logging.error(f"流水线执行失败: {e}")
        # 可以在这里添加错误恢复逻辑
        raise
    
    logging.info("整个流水线执行完成")
```

## 监控和度量

### 处理时间统计
```python
import time

def time_stage(stage_name, func, *args, **kwargs):
    """记录阶段执行时间"""
    start_time = time.time()
    logging.info(f"开始{stage_name}...")
    
    result = func(*args, **kwargs)
    
    end_time = time.time()
    duration = end_time - start_time
    logging.info(f"{stage_name}完成，耗时: {duration:.2f}秒")
    
    return result

# 使用示例
# result = time_stage("需求提取", extractor.extract_requirements, example_file)
```

### 资源使用监控
```python
import psutil
import os

def log_resource_usage(stage_name):
    """记录资源使用情况"""
    process = psutil.Process(os.getpid())
    memory_mb = process.memory_info().rss / 1024 / 1024
    cpu_percent = process.cpu_percent()
    
    logging.info(f"{stage_name} - 内存使用: {memory_mb:.2f}MB, CPU: {cpu_percent:.1f}%")
```

通过 `__main__.py` 的完整流水线设计，整个需求分析过程实现了端到端的自动化，为软件产品线的需求分析和可变性管理提供了强大的工具支持。
